// app.js
import storage from './utils/storage.js'
import dataInitializer from './utils/data-initializer.js'
import dbInitializer from './utils/db-initializer.js'
import cloudFunctionManager from './utils/cloud-function-manager.js'
import simpleDataManager from './utils/simple-data-manager.js'
import systemDemo from './utils/system-demo.js'

App({
  onLaunch() {
    const self = this

    // 设置全局错误处理
    this.setupGlobalErrorHandling()

    // 初始化云开发
    this.initCloud()

    // 初始化简化数据管理器
    this.initSimpleDataManager()

    // 初始化小程序
    this.initApp()

    // 检查登录状态
    this.checkLoginStatus()

    // 延迟初始化应用数据，确保云开发已完全初始化
    setTimeout(function() {
      self.initAppData()
    }, 1000)
  },

  // 初始化应用数据
  async initAppData() {
    const self = this

    try {
      // 初始化数据库
      const dbInitResult = await dbInitializer.initialize()

      if (!dbInitResult) {
        console.warn('数据库初始化失败，使用默认配置')
      }

      // 然后初始化应用数据
      dataInitializer.initializeAppData(function(error) {
        if (!error) {
          // 检查数据完整性
          dataInitializer.checkDataIntegrity(function(error, isIntact) {
            if (error) {
              console.warn('数据完整性检查失败:', error)
            } else if (!isIntact) {
              console.warn('数据完整性异常，建议用户重新登录')
            }
          })

          // 启动数据预加载（仅在用户已登录时）
          if (self.globalData.isLoggedIn) {
            self.preloadAppData()
          }
        }
      })
    } catch (error) {
      console.error('应用数据初始化失败:', error)
      // 继续启动应用，但记录错误
    }
  },



  // 预加载应用数据 - 使用简化数据管理器
  preloadAppData() {
    // 简化数据管理器会自动处理数据预热
    setTimeout(async () => {
      try {
        // 预热关键数据（仅从缓存获取，不调用云函数）
        const result = await simpleDataManager.getData('user_info', null, { useCache: true })
        if (result.success) {
          console.log('数据预热完成，来源:', result.fromCache ? '内存缓存' : result.fromLocal ? '本地存储' : '未知')
        } else {
          console.log('数据预热：没有缓存数据，等待用户操作时加载')
        }
      } catch (error) {
        console.warn('数据预加载失败:', error)
      }
    }, 100)
  },

  // 初始化简化数据管理器
  async initSimpleDataManager() {
    try {
      // 初始化数据管理器
      await simpleDataManager.initialize()

      console.log('✅ 简化数据管理器初始化成功')

      // 开发环境下启动演示
      if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
        // 延迟启动演示，避免影响应用启动
        setTimeout(() => {
          console.log('🎯 启动系统演示...')
          systemDemo.runFullDemo()
        }, 3000)
      }

    } catch (error) {
      console.error('简化数据管理器初始化失败:', error)
    }
  },

  // 初始化云开发
  initCloud() {
    if (wx.cloud) {
      wx.cloud.init({
        env: 'cloud1-8gazcu0g1d5b8a5a', // 替换为你的云开发环境ID
        traceUser: true
      })

      // 初始化云函数调用管理器
      cloudFunctionManager.init()

      // 添加云函数调用监控（开发环境）
      if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
        this.setupCloudFunctionMonitor()
      }

      // 定期清理缓存
      setInterval(() => {
        cloudFunctionManager.cleanup()
      }, 60000) // 每分钟清理一次
    }
  },

  // 云函数调用监控（与管理器配合）
  setupCloudFunctionMonitor() {
    // 定期输出统计信息（降低频率，减少日志输出）
    setInterval(() => {
      const stats = cloudFunctionManager.getStats()
      // 只在有显著变化时输出统计
      if (stats.totalCalls > 0 && stats.totalCalls % 10 === 0) {
        console.log('[云函数统计]', {
          总调用: stats.totalCalls,
          被阻止: stats.blockedCalls,
          缓存命中: stats.cacheHits,
          错误: stats.errors,
          缓存命中率: `${(stats.cacheHitRate * 100).toFixed(1)}%`,
          阻止率: `${(stats.blockRate * 100).toFixed(1)}%`,
          待处理: stats.pendingCalls,
          缓存大小: stats.cacheSize
        })
      }
    }, 120000) // 改为每2分钟输出一次统计
  },

  // 初始化应用
  initApp() {
    // 获取系统信息（使用新API）
    try {
      const systemInfo = {
        ...wx.getWindowInfo(),
        ...wx.getDeviceInfo(),
        ...wx.getAppBaseInfo()
      }
      this.globalData.systemInfo = systemInfo
    } catch (error) {
      // 降级使用旧API
      this.globalData.systemInfo = wx.getSystemInfoSync()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = storage.getUserInfo()

    if (userInfo && (userInfo.openid || userInfo.phone)) {
      // 用户已登录，设置全局用户信息
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      this.globalData.openid = userInfo.openid
    } else {
      // 用户未登录
      this.globalData.userInfo = null
      this.globalData.isLoggedIn = false
      this.globalData.openid = null
    }
  },

  // 添加全局错误处理方法
  setupGlobalErrorHandling() {
    // 捕获未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      console.error('未处理的Promise拒绝:', res.reason)
      this.showUserFriendlyError('操作失败，请重试')
    })

    // 捕获JS错误
    wx.onError((error) => {
      console.error('全局JS错误:', error)
      this.showUserFriendlyError('应用出现异常，正在恢复')
    })
  },

  // 用户友好的错误提示
  showUserFriendlyError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    isLoggedIn: false,
    openid: null,

    // 开发工具方法
    testCloudFunctionManager: () => {
      return cloudFunctionManager.testRateLimit()
    },
    getCloudFunctionStats: () => {
      return cloudFunctionManager.getDetailedStatus()
    }
  }
})
