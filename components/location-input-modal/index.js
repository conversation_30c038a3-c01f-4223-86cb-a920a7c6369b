// components/location-input-modal/index.js
// 地点输入弹窗组件

Component({
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '你想去哪里？'
    },
    // 初始值
    value: {
      type: String,
      value: ''
    },
    // 是否显示热门城市
    showHotCities: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 输入值
    inputValue: '',
    
    // 搜索建议列表
    suggestions: [],
    
    // 是否显示建议列表
    showSuggestions: false,
    
    // 加载状态
    loading: false,
    
    // 热门城市列表 - 基于2024年官方旅游数据
    hotCities: [
      { name: '北京', province: '北京', country: '中国' },
      { name: '上海', province: '上海', country: '中国' },
      { name: '成都', province: '四川', country: '中国' },
      { name: '广州', province: '广东', country: '中国' },
      { name: '深圳', province: '广东', country: '中国' },
      { name: '西安', province: '陕西', country: '中国' },
      { name: '杭州', province: '浙江', country: '中国' },
      { name: '重庆', province: '重庆', country: '中国' },
      { name: '南京', province: '江苏', country: '中国' },
      { name: '苏州', province: '江苏', country: '中国' },
      { name: '武汉', province: '湖北', country: '中国' },
      { name: '长沙', province: '湖南', country: '中国' },
      { name: '厦门', province: '福建', country: '中国' },
      { name: '青岛', province: '山东', country: '中国' },
      { name: '大连', province: '辽宁', country: '中国' },
      { name: '天津', province: '天津', country: '中国' },
      { name: '昆明', province: '云南', country: '中国' },
      { name: '哈尔滨', province: '黑龙江', country: '中国' },
      { name: '沈阳', province: '辽宁', country: '中国' },
      { name: '济南', province: '山东', country: '中国' },
      { name: '福州', province: '福建', country: '中国' },
      { name: '珠海', province: '广东', country: '中国' },
      { name: '三亚', province: '海南', country: '中国' },
      { name: '桂林', province: '广西', country: '中国' }
    ],
    
    // 最近搜索
    recentSearches: []
  },

  lifetimes: {
    attached() {
      this.loadRecentSearches()
    }
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.setData({
          inputValue: this.properties.value
        })
        this.loadRecentSearches()
      }
    }
  },

  methods: {
    /**
     * 输入事件处理
     */
    onInput(e) {
      const value = e.detail.value
      this.setData({
        inputValue: value
      })

      // 防抖搜索
      this.performSearch(value)
    },

    /**
     * 执行搜索
     */
    performSearch(keyword) {
      if (!keyword || keyword.trim().length === 0) {
        this.setData({
          suggestions: [],
          showSuggestions: false
        })
        return
      }

      this.setData({ loading: true })

      // 使用腾讯位置服务进行搜索
      tencentLocationSearch.debouncedSuggestion(
        keyword,
        (result) => {
          this.setData({
            loading: false,
            suggestions: result.data || [],
            showSuggestions: result.success && result.data.length > 0
          })
        },
        {
          policy: 1, // 使用收货地址策略，提高小区、商务楼宇等排序
          page_size: 8 // 限制返回8条结果
        }
      )
    },

    /**
     * 选择搜索建议
     */
    selectSuggestion(e) {
      const suggestion = e.currentTarget.dataset.suggestion
      this.selectLocation(suggestion)
    },

    /**
     * 选择热门城市
     */
    selectHotCity(e) {
      const city = e.currentTarget.dataset.city
      const location = {
        name: city.name,
        address: `${city.province}${city.name}`,
        province: city.province,
        city: city.name,
        country: city.country,
        location: {
          latitude: 0, // 热门城市坐标需要通过地址解析获取
          longitude: 0
        }
      }
      this.selectLocation(location)
    },

    /**
     * 选择最近搜索
     */
    selectRecentSearch(e) {
      const recent = e.currentTarget.dataset.recent
      this.selectLocation(recent)
    },

    /**
     * 选择地点
     */
    selectLocation(location) {
      // 保存到最近搜索
      this.saveToRecentSearches(location)
      
      // 触发选择事件
      this.triggerEvent('select', {
        location: location
      })
      
      // 关闭弹窗
      this.close()
    },

    /**
     * 打开地图选点 - 优化权限处理
     */
    openMapPicker() {
      // 先检查权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation'] === false) {
            // 用户拒绝过权限，引导用户手动开启
            wx.showModal({
              title: '位置权限',
              content: '地图选点需要位置权限，请在设置中开启',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting()
                }
              }
            })
            return
          }

          // 打开地图选点
          wx.chooseLocation({
            success: (res) => {
              const location = {
                name: res.name || res.address || '未知地点',
                address: res.address || '',
                location: {
                  latitude: res.latitude,
                  longitude: res.longitude
                }
              }
              this.selectLocation(location)
            },
            fail: (err) => {
              console.error('地图选点失败:', err)

              if (err.errMsg.includes('cancel')) {
                return // 用户取消，不显示错误
              }

              if (err.errMsg.includes('auth')) {
                wx.showModal({
                  title: '位置权限',
                  content: '地图选点需要位置权限，请允许位置权限',
                  confirmText: '重新授权',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      this.openMapPicker()
                    }
                  }
                })
              } else {
                wx.showToast({
                  title: '选择位置失败，请重试',
                  icon: 'none'
                })
              }
            }
          })
        }
      })
    },





    /**
     * 加载最近搜索
     */
    loadRecentSearches() {
      try {
        const recent = wx.getStorageSync('recent_location_searches') || []
        this.setData({
          recentSearches: recent.slice(0, 5)
        })
      } catch (error) {
        console.error('加载最近搜索失败:', error)
      }
    },

    /**
     * 保存到最近搜索
     */
    saveToRecentSearches(location) {
      try {
        const recent = wx.getStorageSync('recent_location_searches') || []
        
        // 移除重复项
        const filtered = recent.filter(item => item.name !== location.name)
        
        // 添加到开头
        filtered.unshift(location)
        
        // 只保留最近5个
        const updated = filtered.slice(0, 5)
        
        wx.setStorageSync('recent_location_searches', updated)
        
        this.setData({
          recentSearches: updated
        })
      } catch (error) {
        console.error('保存最近搜索失败:', error)
      }
    },

    /**
     * 清空输入
     */
    clearInput() {
      this.setData({
        inputValue: '',
        suggestions: [],
        showSuggestions: false
      })
    },

    /**
     * 关闭弹窗
     */
    close() {
      this.triggerEvent('close')
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击弹窗内容区域时关闭弹窗
    }
  }
})
