<!--components/location-input-modal/index.wxml-->
<view wx:if="{{show}}" class="location-modal-overlay" bindtap="close">
  <view class="location-modal-container" catchtap="stopPropagation">
    
    <!-- 弹窗头部 -->
    <view class="modal-header">
      <view class="header-title">{{placeholder}}</view>
      <view class="header-close" bindtap="close">
        <custom-icon name="close" size="20" color="#666" />
      </view>
    </view>

    <!-- 搜索输入框 -->
    <view class="search-section">
      <view class="search-input-container">
        <view class="search-icon">
          <custom-icon name="search" size="18" color="#999" />
        </view>
        <input
          class="search-input"
          placeholder="搜索城市或地点"
          value="{{inputValue}}"
          bindinput="onInput"
          focus="{{show}}"
          confirm-type="search"
        />
        <view wx:if="{{inputValue}}" class="clear-icon" bindtap="clearInput">
          <custom-icon name="close" size="16" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 搜索建议列表 -->
    <view wx:if="{{showSuggestions}}" class="suggestions-section">
      <scroll-view class="suggestions-list" scroll-y="true" enhanced="true">
        <view
          wx:for="{{suggestions}}"
          wx:key="id"
          class="suggestion-item"
          bindtap="selectSuggestion"
          data-suggestion="{{item}}"
        >
          <view class="suggestion-icon">
            <custom-icon name="location" size="16" color="#4ECDC4" />
          </view>
          <view class="suggestion-content">
            <view class="suggestion-title">{{item.title}}</view>
            <view class="suggestion-address">{{item.address}}</view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 默认内容区域 -->
    <view wx:else class="content-section">
      <scroll-view class="content-scroll" scroll-y="true" enhanced="true">
        
        <!-- 快速操作 -->
        <view class="quick-actions">
          <view class="quick-action-item map-only" bindtap="openMapPicker">
            <view class="action-icon map-picker">
              <custom-icon name="map" size="20" color="#fff" />
            </view>
            <text class="action-text">地图选点</text>
          </view>
        </view>

        <!-- 最近搜索 -->
        <view wx:if="{{recentSearches.length > 0}}" class="recent-section">
          <view class="section-title">最近搜索</view>
          <view class="recent-list">
            <view
              wx:for="{{recentSearches}}"
              wx:key="name"
              class="recent-item"
              bindtap="selectRecentSearch"
              data-recent="{{item}}"
            >
              <view class="recent-icon">
                <custom-icon name="clock" size="14" color="#999" />
              </view>
              <view class="recent-content">
                <view class="recent-name">{{item.name}}</view>
                <view class="recent-address">{{item.address}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 热门城市 -->
        <view wx:if="{{showHotCities}}" class="hot-cities-section">
          <view class="section-title">热门城市</view>
          <view class="hot-cities-grid">
            <view
              wx:for="{{hotCities}}"
              wx:key="name"
              class="hot-city-item"
              bindtap="selectHotCity"
              data-city="{{item}}"
            >
              <text class="city-name">{{item.name}}</text>
            </view>
          </view>
        </view>

      </scroll-view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-overlay">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">搜索中...</text>
      </view>
    </view>

  </view>
</view>
