/* components/location-input-modal/index.wxss */

.location-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease-out;
}

.location-modal-container {
  width: 100%;
  max-height: 80vh;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

/* 搜索区域 */
.search-section {
  padding: 16rpx 32rpx 24rpx;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
}

.search-icon {
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  background: transparent;
}

.search-input::placeholder {
  color: #999;
}

.clear-icon {
  margin-left: 16rpx;
  padding: 8rpx;
}

/* 搜索建议列表 */
.suggestions-section {
  flex: 1;
  min-height: 0;
}

.suggestions-list {
  max-height: 60vh;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  margin-right: 24rpx;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.suggestion-address {
  font-size: 26rpx;
  color: #666;
}

/* 内容区域 */
.content-section {
  flex: 1;
  min-height: 0;
}

.content-scroll {
  max-height: 60vh;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  justify-content: center;
  padding: 24rpx 32rpx;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 48rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  min-width: 200rpx;
}

.quick-action-item.map-only {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(69, 183, 209, 0.1));
  border: 2rpx solid rgba(78, 205, 196, 0.3);
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.action-icon.map-picker {
  background: linear-gradient(135deg, #4ECDC4, #45B7D1);
}

.action-text {
  font-size: 26rpx;
  color: #666;
}

/* 区域标题 */
.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 24rpx 32rpx 16rpx;
  font-weight: 500;
}

/* 最近搜索 */
.recent-section {
  margin-top: 16rpx;
}

.recent-list {
  padding: 0 32rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-icon {
  margin-right: 20rpx;
}

.recent-content {
  flex: 1;
}

.recent-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.recent-address {
  font-size: 24rpx;
  color: #999;
}

/* 热门城市 */
.hot-cities-section {
  margin-top: 16rpx;
  padding-bottom: 32rpx;
}

.hot-cities-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 32rpx;
  gap: 12rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.hot-city-item {
  flex: 0 0 calc(20% - 10rpx);
  padding: 16rpx 8rpx;
  background: linear-gradient(135deg,
    rgba(78, 205, 196, 0.08) 0%,
    rgba(69, 183, 209, 0.06) 100%
  );
  border: 1rpx solid rgba(78, 205, 196, 0.2);
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
}

.hot-city-item:active {
  background: linear-gradient(135deg,
    rgba(78, 205, 196, 0.15) 0%,
    rgba(69, 183, 209, 0.12) 100%
  );
  border-color: rgba(78, 205, 196, 0.4);
  transform: scale(0.98);
}

.city-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式适配 */
@media (max-height: 600px) {
  .location-modal-container {
    max-height: 90vh;
  }
  
  .suggestions-list,
  .content-scroll {
    max-height: 50vh;
  }
}
