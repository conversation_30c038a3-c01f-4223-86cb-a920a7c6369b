/**
 * 微信云托管WebSocket协作服务
 * 基于官方示例，使用express-ws实现
 * 支持微信小程序实时协作
 */

const express = require('express')
const expressWs = require('express-ws')

// 初始化微信云开发
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const app = express()

// 初始化express-ws（基于官方示例）
const wsInstance = expressWs(app)

// 中间件配置
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 连接管理
const connections = new Map() // 存储所有WebSocket连接
const rooms = new Map() // 房间管理
const userSessions = new Map() // 用户会话管理

// 统计信息
const stats = {
  totalConnections: 0,
  activeConnections: 0,
  totalMessages: 0,
  roomCount: 0,
  startTime: Date.now()
}

// 心跳配置
const HEARTBEAT_INTERVAL = 30000 // 30秒

/**
 * 健康检查接口
 */
app.get('/', (req, res) => {
  // 减少日志输出
  res.json({
    service: 'WebSocket协作服务',
    status: 'running',
    version: '1.0.3',
    timestamp: new Date().toISOString(),
    stats: {
      ...stats,
      uptime: Date.now() - stats.startTime,
      rooms: Array.from(rooms.keys()),
      activeConnections: connections.size
    }
  })
})

/**
 * 健康检查接口
 */
app.get('/health', (req, res) => {
  // 健康检查请求
  res.json({
    status: 'ok',
    time: Date.now(),
    service: 'websocket-collaboration'
  })
})

/**
 * 服务状态接口
 */
app.get('/status', (req, res) => {
  // 状态查询请求
  res.json({
    service: 'WebSocket协作服务',
    status: 'running',
    port: process.env.PORT || 80,
    connections: connections.size,
    rooms: rooms.size,
    timestamp: new Date().toISOString()
  })
})

/**
 * 调试接口
 */
app.get('/debug', (req, res) => {
  // 调试请求
  res.json({
    port: process.env.PORT || 80,
    env: process.env.NODE_ENV || 'production',
    connections: connections.size,
    rooms: rooms.size,
    headers: req.headers
  })
})

/**
 * 查询当前连接的用户（基于官方示例）
 */
app.post('/query', (req, res) => {
  const connectedUsers = []
  for (const [id, conn] of connections.entries()) {
    if (conn.ws.readyState === 1) {
      connectedUsers.push({
        connectionId: id,
        openid: conn.openid,
        joinTime: conn.joinTime,
        roomId: conn.roomId
      })
    }
  }

  res.json({
    success: true,
    data: connectedUsers,
    count: connectedUsers.length
  })
})

/**
 * WebSocket连接处理（基于官方示例）
 */
app.ws('/ws', (ws, req) => {
  // 从请求头获取微信用户信息（云托管自动注入）
  const openid = req.headers['x-wx-openid'] || 'anonymous'
  const unionid = req.headers['x-wx-unionid'] || ''

  // WebSocket连接请求

  // 检查是否已有连接（防止重复连接）
  let existingConnection = null
  for (const [id, conn] of connections.entries()) {
    if (conn.openid === openid && conn.ws.readyState === 1) {
      existingConnection = conn
      break
    }
  }

  if (existingConnection) {
    // 用户已有连接，拒绝新连接
    ws.close(1008, '用户已有连接')
    return
  }

  // 生成连接ID
  const connectionId = generateConnectionId()

  // 连接信息
  const connectionInfo = {
    id: connectionId,
    openid,
    unionid,
    ws,
    isAlive: true,
    joinTime: Date.now(),
    lastActivity: Date.now(),
    roomId: null
  }

  // 存储连接
  connections.set(connectionId, connectionInfo)
  stats.totalConnections++
  stats.activeConnections++

  // 新连接建立

  // 发送连接成功消息
  sendToConnection(connectionId, {
    type: 'connected',
    data: {
      connectionId,
      openid,
      serverTime: Date.now(),
      message: 'WebSocket连接成功'
    }
  })

  // 消息处理
  ws.on('message', (data) => {
    handleMessage(connectionId, data)
  })

  // 连接关闭
  ws.on('close', (code, reason) => {
    handleDisconnection(connectionId, code, reason)
  })

  // 连接错误
  ws.on('error', (error) => {
    // 连接错误
    handleDisconnection(connectionId, 1006, 'error')
  })

  // 心跳响应
  ws.on('pong', () => {
    const connection = connections.get(connectionId)
    if (connection) {
      connection.isAlive = true
      connection.lastActivity = Date.now()
    }
  })
})

/**
 * 处理消息
 */
function handleMessage(connectionId, data) {
  try {
    const connection = connections.get(connectionId)
    if (!connection) return

    connection.lastActivity = Date.now()
    stats.totalMessages++

    const message = JSON.parse(data.toString())
    
    // 根据消息类型处理
    switch (message.type) {
      case 'heartbeat':
        handleHeartbeat(connectionId, message)
        break
      case 'join_room':
        handleJoinRoom(connectionId, message)
        break
      case 'leave_room':
        handleLeaveRoom(connectionId, message)
        break
      case 'travel_update':
        handleTravelUpdate(connectionId, message)
        break
      case 'expense_update':
        handleExpenseUpdate(connectionId, message)
        break
      case 'collaboration_message':
        handleCollaborationMessage(connectionId, message)
        break
      case 'expense_sync_request':
        handleExpenseSyncRequest(connectionId, message)
        break
      case 'expense_batch_sync_request':
        handleExpenseBatchSyncRequest(connectionId, message)
        break
      default:
        console.warn(`未知消息类型: ${message.type}`)
    }

  } catch (error) {
    // 消息处理错误
  }
}

/**
 * 处理心跳
 */
function handleHeartbeat(connectionId, message) {
  sendToConnection(connectionId, {
    type: 'heartbeat',
    data: { pong: Date.now() }
  })
}

/**
 * 处理加入房间
 */
function handleJoinRoom(connectionId, message) {
  const { roomId, userInfo } = message.data
  joinRoom(connectionId, roomId, userInfo)
}

/**
 * 处理离开房间
 */
function handleLeaveRoom(connectionId, message) {
  const { roomId } = message.data
  leaveRoom(connectionId, roomId)
}

/**
 * 处理旅行计划更新
 */
function handleTravelUpdate(connectionId, message) {
  const connection = connections.get(connectionId)
  if (!connection || !connection.roomId) return

  // 广播给房间内其他用户
  broadcastToRoom(connection.roomId, {
    type: 'travel_update',
    data: {
      ...message.data,
      openid: connection.openid,
      timestamp: Date.now()
    }
  }, connectionId)
}

/**
 * 处理支出更新
 */
function handleExpenseUpdate(connectionId, message) {
  const connection = connections.get(connectionId)
  if (!connection || !connection.roomId) return

  // 广播给房间内其他用户
  broadcastToRoom(connection.roomId, {
    type: 'expense_update',
    data: {
      ...message.data,
      openid: connection.openid,
      timestamp: Date.now()
    }
  }, connectionId)
}

/**
 * 处理协作消息
 */
function handleCollaborationMessage(connectionId, message) {
  const connection = connections.get(connectionId)
  if (!connection || !connection.roomId) return

  // 广播给房间内其他用户
  broadcastToRoom(connection.roomId, {
    type: 'collaboration_message',
    data: {
      ...message.data,
      openid: connection.openid,
      timestamp: Date.now()
    }
  }, connectionId)
}

/**
 * 加入房间
 */
function joinRoom(connectionId, roomId, userInfo = {}) {
  if (!rooms.has(roomId)) {
    rooms.set(roomId, new Set())
    stats.roomCount++
  }

  const room = rooms.get(roomId)
  room.add(connectionId)

  const connection = connections.get(connectionId)
  if (connection) {
    connection.roomId = roomId
    connection.userInfo = userInfo
  }

  // 连接加入房间

  // 通知房间内其他用户
  broadcastToRoom(roomId, {
    type: 'user_joined',
    data: {
      openid: connection?.openid,
      userInfo,
      roomId,
      timestamp: Date.now()
    }
  }, connectionId)
}

/**
 * 离开房间
 */
function leaveRoom(connectionId, roomId) {
  const room = rooms.get(roomId)
  if (room) {
    room.delete(connectionId)
    
    if (room.size === 0) {
      rooms.delete(roomId)
      stats.roomCount--
    }
  }

  const connection = connections.get(connectionId)
  if (connection) {
    connection.roomId = null
  }

  // 连接离开房间

  // 通知房间内其他用户
  broadcastToRoom(roomId, {
    type: 'user_left',
    data: {
      openid: connection?.openid,
      roomId,
      timestamp: Date.now()
    }
  }, connectionId)
}

/**
 * 处理连接断开
 */
function handleDisconnection(connectionId, code, reason) {
  const connection = connections.get(connectionId)
  if (!connection) return

  // 连接断开

  // 离开房间
  if (connection.roomId) {
    leaveRoom(connectionId, connection.roomId)
  }

  // 移除连接
  connections.delete(connectionId)
  stats.activeConnections--
}

/**
 * 发送消息给指定连接
 */
function sendToConnection(connectionId, message) {
  const connection = connections.get(connectionId)
  if (!connection || connection.ws.readyState !== 1) return false

  try {
    connection.ws.send(JSON.stringify(message))
    return true
  } catch (error) {
    // 发送消息失败
    return false
  }
}

/**
 * 广播消息给房间内所有用户
 */
function broadcastToRoom(roomId, message, excludeConnectionId = null) {
  const room = rooms.get(roomId)
  if (!room) return

  let sentCount = 0
  room.forEach(connectionId => {
    if (connectionId !== excludeConnectionId) {
      if (sendToConnection(connectionId, message)) {
        sentCount++
      }
    }
  })

  return sentCount
}

/**
 * 心跳检测
 */
function startHeartbeat() {
  setInterval(() => {
    connections.forEach((connection, connectionId) => {
      if (!connection.isAlive) {
        // 心跳超时，断开连接
        connection.ws.terminate()
        handleDisconnection(connectionId, 1006, 'heartbeat timeout')
        return
      }

      connection.isAlive = false
      connection.ws.ping()
    })
  }, HEARTBEAT_INTERVAL)
}

/**
 * 处理记账同步请求
 */
async function handleExpenseSyncRequest(connectionId, message) {
  try {
    const { localId, recordData } = message.data
    const connection = connections.get(connectionId)

    if (!connection) return

    // 处理记账同步请求

    // 调用实际的云函数保存数据
    try {
      console.log('WebSocket服务端调用云函数，recordData:', JSON.stringify(recordData, null, 2))

      // 调用微信云开发云函数
      const result = await cloud.callFunction({
        name: 'expense',
        data: {
          action: 'addExpenseRecord',
          data: recordData
        }
      })

      console.log('云函数调用结果:', JSON.stringify(result, null, 2))

      if (result.result && result.result.success) {
        const cloudId = result.result.data.id
        console.log('记账同步成功，cloudId:', cloudId)

      // 发送同步确认
      sendToConnection(connectionId, {
        type: 'expense_sync_confirm',
        data: {
          localId,
          success: true,
          cloudId,
          timestamp: Date.now()
        }
      })

        // 记账同步成功
      } else {
        console.error('云函数返回失败结果:', result.result)
        throw new Error(result.result?.message || '云函数调用失败')
      }

    } catch (error) {
      console.error('WebSocket服务端云函数调用异常:', error)

      // 发送同步失败确认
      sendToConnection(connectionId, {
        type: 'expense_sync_confirm',
        data: {
          localId,
          success: false,
          error: error.message,
          timestamp: Date.now()
        }
      })

      // 记账同步失败
    }

  } catch (error) {
    // 处理记账同步请求失败
  }
}

/**
 * 处理批量记账同步请求
 */
async function handleExpenseBatchSyncRequest(connectionId, message) {
  try {
    const { records } = message.data
    const connection = connections.get(connectionId)

    if (!connection) return

    // 处理批量记账同步请求

    // 批量处理记录
    for (const record of records) {
      try {
        // 调用实际的云函数保存数据
        const result = await cloud.callFunction({
          name: 'expense',
          data: {
            action: 'addExpenseRecord',
            data: record.recordData
          }
        })

        if (result.result && result.result.success) {
          const cloudId = result.result.data.id

          // 发送单条同步确认
          sendToConnection(connectionId, {
            type: 'expense_sync_confirm',
            data: {
              localId: record.localId,
              success: true,
              cloudId,
              timestamp: Date.now()
            }
          })
        } else {
          throw new Error(result.result?.message || '云函数调用失败')
        }

        // 避免过于频繁的响应
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        // 发送失败确认
        sendToConnection(connectionId, {
          type: 'expense_sync_confirm',
          data: {
            localId: record.localId,
            success: false,
            error: error.message,
            timestamp: Date.now()
          }
        })
      }
    }

    // 批量记账同步完成

  } catch (error) {
    // 处理批量记账同步请求失败
  }
}

/**
 * 生成连接ID
 */
function generateConnectionId() {
  return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 启动心跳检测
startHeartbeat()

// 启动服务器
const PORT = process.env.PORT || 80
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`微信云托管WebSocket服务启动成功，端口: ${PORT}`)
  console.log(`服务状态: http://localhost:${PORT}/`)
  console.log(`监听地址: 0.0.0.0:${PORT}`)
})

// 监听服务器错误
server.on('error', (error) => {
  // 服务器启动错误
})

// 优雅关闭
process.on('SIGTERM', () => {
  // 收到SIGTERM信号，正在关闭服务器
  server.close(() => {
    process.exit(0)
  })
})
