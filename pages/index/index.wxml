<!--pages/index/index.wxml-->
<view class="home-container">
  <!-- 用户欢迎区域 -->
  <view class="welcome-area">
    <view class="user-greeting">
      <view class="online-status"></view>
      <view class="greeting-text">
        <text class="greeting-line1">你好，</text>
        <text class="greeting-line2">{{userInfo.nickName === '微信用户' ? '爱巢用户' : (userInfo.nickName || '爱巢用户')}}</text>
      </view>
    </view>
    <text class="welcome-subtitle">开始今天的精彩旅程吧</text>

    <!-- 右侧用户操作区 -->
    <view class="user-actions">
      <view class="notification-icon" bindtap="navigateToNotifications">
        <image src="/images/bell.svg" class="icon-medium" mode="aspectFit" />
        <view wx:if="{{hasNotification}}" class="notification-badge">{{notificationCount || 1}}</view>
      </view>
      <view class="user-avatar" bindtap="navigateToProfile" bindlongpress="openDebugMenu">
        <van-image
          src="{{userInfo.avatarUrl || '/images/user.svg'}}"
          mode="aspectFill"
          round
          width="80rpx"
          height="80rpx"
        />
      </view>
    </view>
  </view>

  <!-- 本月财务概览 -->
  <view class="finance-card glass-readable-normal">
    <view class="card-header">
      <text class="card-title glass-text-primary">本月财务概览</text>
      <view class="setting-btn" bindtap="navigateToBudgetSetting">
        <text class="glass-text-brand">设置预算</text>
      </view>
    </view>

    <view class="main-amount">
      <text class="amount-currency glass-text-primary">¥</text>
      <text class="amount-value glass-text-primary">{{financialOverview.totalExpense || 0}}</text>
      <text class="amount-label glass-text-secondary">本月总支出</text>
    </view>

    <!-- 支出分类 -->
    <view class="expense-categories">
      <view class="category-item">
        <text class="category-label glass-text-secondary">日常支出</text>
        <text class="category-amount glass-text-primary">¥{{financialOverview.dailyExpense || 0}}</text>
        <text class="category-percent glass-text-secondary">{{financialOverview.dailyPercentage || 0}}%</text>
      </view>
      <view class="category-item">
        <text class="category-label glass-text-secondary">旅行支出</text>
        <text class="category-amount glass-text-primary">¥{{financialOverview.travelExpense || 0}}</text>
        <text class="category-percent glass-text-secondary">{{financialOverview.travelPercentage || 0}}%</text>
      </view>
      <view class="category-item">
        <text class="category-label glass-text-secondary">剩余预算</text>
        <text class="category-amount glass-text-primary">¥{{financialOverview.remainingBudget || 0}}</text>
        <text class="category-percent glass-text-secondary">{{financialOverview.remainingPercentage || 0}}%</text>
      </view>
    </view>

    <!-- 预算使用进度 -->
    <view class="budget-progress">
      <view class="progress-header">
        <text class="progress-label glass-text-primary">{{financialOverview.usagePercentage || 0}}% 已使用</text>
        <text class="progress-remaining glass-text-secondary">剩余 ¥{{financialOverview.remainingBudget || 0}}</text>
      </view>
      <view class="progress-bar-container">
        <view class="progress-bar">
          <view
            class="progress-fill"
            style="width: {{financialOverview.usagePercentage || 0}}%">
          </view>
        </view>
      </view>
    </view>


  </view>

  <!-- 功能模块 -->
  <view class="feature-modules">
    <view class="module-row">
      <view class="module-item glass-readable-subtle glass-btn-interactive" bindtap="navigateToTravelPlanning">
        <view class="module-icon travel">
          <view class="icon-container">
            <custom-icon name="airplane" size="54" color="#45B7D1" />
          </view>
        </view>
        <view class="module-content">
          <text class="module-title glass-text-primary">旅行规划</text>
          <text class="module-desc glass-text-secondary">创建行程计划</text>
        </view>
      </view>

      <view class="module-item glass-readable-subtle glass-btn-interactive" bindtap="navigateToAccounting">
        <view class="module-icon accounting">
          <view class="icon-container">
            <custom-icon name="wallet" size="54" color="#FF6B6B" />
          </view>
        </view>
        <view class="module-content">
          <text class="module-title glass-text-primary">智能记账</text>
          <text class="module-desc glass-text-secondary">预算管理</text>
        </view>
      </view>
    </view>

    <view class="module-row">
      <view class="module-item glass-readable-subtle glass-btn-interactive" bindtap="navigateToSocial">
        <view class="module-icon social">
          <view class="icon-container">
            <custom-icon name="heart" size="54" color="#ff9a9e" />
          </view>
        </view>
        <view class="module-content">
          <text class="module-title glass-text-primary">好友分享</text>
          <text class="module-desc glass-text-secondary">旅行社交</text>
        </view>
      </view>

      <view class="module-item glass-readable-subtle glass-btn-interactive" bindtap="navigateToDiscover">
        <view class="module-icon discover">
          <view class="icon-container">
            <custom-icon name="search" size="54" color="#667eea" />
          </view>
        </view>
        <view class="module-content">
          <text class="module-title glass-text-primary">发现</text>
          <text class="module-desc glass-text-secondary">热门目的地</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Vant Toast 组件 -->
  <van-toast id="van-toast" />
</view>
