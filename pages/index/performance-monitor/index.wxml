<!--pages/performance-monitor/index.wxml-->
<view class="performance-monitor">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">性能监控</text>
    <text class="subtitle">极简主义数据管理系统</text>
  </view>

  <!-- 性能指标卡片 -->
  <view class="metrics-section">
    <view class="section-title">核心指标</view>
    <view class="metrics-grid">
      <view class="metric-card">
        <view class="metric-value {{getPerformanceLevel(performanceMetrics.cacheHitRate)}}">
          {{performanceMetrics.cacheHitRate.toFixed(1)}}%
        </view>
        <view class="metric-label">缓存命中率</view>
      </view>
      
      <view class="metric-card">
        <view class="metric-value {{getPerformanceLevel(performanceMetrics.avgResponseTime, [100, 200, 500])}}">
          {{performanceMetrics.avgResponseTime}}ms
        </view>
        <view class="metric-label">平均响应时间</view>
      </view>
      
      <view class="metric-card">
        <view class="metric-value {{performanceMetrics.syncQueueSize > 10 ? 'poor' : 'good'}}">
          {{performanceMetrics.syncQueueSize}}
        </view>
        <view class="metric-label">同步队列</view>
      </view>
      
      <view class="metric-card">
        <view class="metric-value {{getPerformanceLevel(performanceMetrics.memoryUsage)}}">
          {{performanceMetrics.memoryUsage}}%
        </view>
        <view class="metric-label">内存使用</view>
      </view>
    </view>
  </view>

  <!-- 系统状态 -->
  <view class="status-section">
    <view class="section-title">系统状态</view>
    
    <!-- 乐观更新状态 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-name">乐观更新管理器</text>
        <view class="status-indicator {{systemStats.optimisticUpdate.queueSize > 0 ? 'active' : 'idle'}}"></view>
      </view>
      <view class="status-details">
        <text>队列大小: {{systemStats.optimisticUpdate.queueSize || 0}}</text>
        <text>历史记录: {{systemStats.optimisticUpdate.historySize || 0}}</text>
        <text>最后同步: {{formatTime(systemStats.optimisticUpdate.lastSyncTime)}}</text>
      </view>
    </view>

    <!-- 分层同步状态 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-name">分层同步管理器</text>
        <view class="status-indicator {{systemStats.layeredSync.total > 0 ? 'active' : 'idle'}}"></view>
      </view>
      <view class="status-details">
        <text>关键操作: {{systemStats.layeredSync.critical || 0}}</text>
        <text>重要操作: {{systemStats.layeredSync.important || 0}}</text>
        <text>一般操作: {{systemStats.layeredSync.general || 0}}</text>
      </view>
    </view>

    <!-- 数据管理器状态 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-name">简化数据管理器</text>
        <view class="status-indicator active"></view>
      </view>
      <view class="status-details">
        <text>内存缓存: {{systemStats.dataManager.memorySize || 0}}/{{systemStats.dataManager.maxMemorySize || 0}}</text>
        <text>本地计算: {{systemStats.dataManager.localCalculations || 0}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="section-title">操作面板</view>
    <view class="actions-grid">
      <button class="action-btn primary" bindtap="runDemo" disabled="{{demoRunning}}">
        {{demoRunning ? '演示运行中...' : '运行系统演示'}}
      </button>
      
      <button class="action-btn" bindtap="testOptimisticUpdate">
        测试乐观更新
      </button>
      
      <button class="action-btn" bindtap="testLayeredSync">
        测试分层同步
      </button>
      
      <button class="action-btn" bindtap="testDataManager">
        测试数据管理器
      </button>
      
      <button class="action-btn warning" bindtap="clearCache">
        清理缓存
      </button>
      
      <button class="action-btn" bindtap="forceSync">
        强制同步
      </button>
    </view>
  </view>

  <!-- 实时日志 -->
  <view class="logs-section">
    <view class="section-header">
      <text class="section-title">实时日志</text>
      <view class="log-actions">
        <button class="mini-btn" bindtap="onRefresh" disabled="{{refreshing}}">
          {{refreshing ? '刷新中' : '刷新'}}
        </button>
        <button class="mini-btn" bindtap="clearLogs">清空</button>
      </view>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true" scroll-top="{{logs.length * 30}}">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        {{item}}
      </view>
      <view wx:if="{{logs.length === 0}}" class="empty-logs">
        暂无日志记录
      </view>
    </scroll-view>
  </view>

  <!-- 性能提示 -->
  <view class="tips-section">
    <view class="section-title">性能优化提示</view>
    <view class="tips-list">
      <view class="tip-item">
        <text class="tip-icon">💡</text>
        <text class="tip-text">缓存命中率 > 80% 表示性能优秀</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">⚡</text>
        <text class="tip-text">乐观更新让用户操作立即响应</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">🔄</text>
        <text class="tip-text">分层同步减少不必要的云函数调用</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">📱</text>
        <text class="tip-text">本地优先策略提升离线体验</text>
      </view>
    </view>
  </view>
</view>
