/* 云函数监控页面样式 */

.monitor-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.btn-refresh, .btn-auto {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
}

.btn-refresh {
  background: #007aff;
  color: white;
}

.btn-refresh.loading {
  background: #ccc;
}

.btn-auto {
  background: #f0f0f0;
  color: #333;
}

.btn-auto.active {
  background: #34c759;
  color: white;
}

/* 告警区域 */
.alerts-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
}

.alerts-list {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.alert-item {
  padding: 24rpx;
  border-left: 8rpx solid;
  border-bottom: 1rpx solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-danger {
  border-left-color: #ff3b30;
  background: #fff5f5;
}

.alert-warning {
  border-left-color: #ff9500;
  background: #fffbf0;
}

.alert-info {
  border-left-color: #007aff;
  background: #f0f8ff;
}

.alert-default {
  border-left-color: #8e8e93;
  background: #f8f8f8;
}

.alert-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.alert-message {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 24rpx;
  color: #999;
}

/* 概览统计 */
.summary-section {
  margin-bottom: 30rpx;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.summary-item {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.summary-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.summary-label {
  font-size: 26rpx;
  color: #666;
}

/* 详细统计 */
.details-section {
  margin-bottom: 30rpx;
}

.details-content {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
}

.stats-group {
  margin-bottom: 30rpx;
}

.stats-group:last-child {
  margin-bottom: 0;
}

.stats-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}

.stats-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 函数统计 */
.function-stats {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.function-item {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
}

.function-item:active {
  border-color: #007aff;
}

.function-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.function-details {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.function-calls {
  color: #007aff;
  font-weight: bold;
}

.function-success {
  color: #34c759;
}

.function-time {
  color: #ff9500;
}

/* 操作按钮 */
.actions-section {
  margin-bottom: 30rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-btn {
  padding: 24rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: none;
  font-weight: bold;
}

.action-btn.test {
  background: #007aff;
  color: white;
}

.action-btn.clear {
  background: #ff9500;
  color: white;
}

.action-btn.reset {
  background: #ff3b30;
  color: white;
}

.action-btn.export {
  background: #34c759;
  color: white;
}

.action-btn:disabled {
  background: #ccc !important;
  color: #999 !important;
}

/* 底部信息 */
.footer {
  background: white;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.footer-text:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    gap: 20rpx;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
}
