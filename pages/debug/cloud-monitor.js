import cloudFunctionManager from '../../utils/cloud-function-manager.js'
import cloudFunctionMonitor from '../../utils/cloud-function-monitor.js'
import simpleDataManager from '../../utils/simple-data-manager.js'

Page({
  data: {
    // 监控数据
    monitorReport: null,
    managerStats: null,
    
    // 刷新状态
    refreshing: false,
    autoRefresh: false,
    refreshInterval: null,
    
    // 显示选项
    showDetails: false,
    selectedFunction: '',
    
    // 告警信息
    alerts: [],
    
    // 测试功能
    testing: false
  },

  onLoad() {
    console.log('云函数监控页面加载')
    this.loadMonitorData()
  },

  onShow() {
    this.loadMonitorData()
  },

  onHide() {
    this.stopAutoRefresh()
  },

  onUnload() {
    this.stopAutoRefresh()
  },

  /**
   * 加载监控数据
   */
  async loadMonitorData() {
    if (this.data.refreshing) return

    this.setData({ refreshing: true })

    try {
      // 获取管理器统计
      const managerStats = cloudFunctionManager.getStats()
      
      // 获取监控器报告
      const monitorReport = cloudFunctionMonitor.getReport()

      this.setData({
        managerStats,
        monitorReport,
        alerts: monitorReport.activeAlerts || []
      })

      console.log('监控数据加载完成:', { managerStats, monitorReport })

    } catch (error) {
      console.error('加载监控数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ refreshing: false })
    }
  },

  /**
   * 手动刷新
   */
  onRefresh() {
    this.loadMonitorData()
  },

  /**
   * 切换自动刷新
   */
  toggleAutoRefresh() {
    const autoRefresh = !this.data.autoRefresh
    this.setData({ autoRefresh })

    if (autoRefresh) {
      this.startAutoRefresh()
    } else {
      this.stopAutoRefresh()
    }
  },

  /**
   * 开始自动刷新
   */
  startAutoRefresh() {
    if (this.data.refreshInterval) return

    const interval = setInterval(() => {
      this.loadMonitorData()
    }, 5000) // 每5秒刷新一次

    this.setData({ refreshInterval: interval })
    console.log('自动刷新已开启')
  },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.data.refreshInterval) {
      clearInterval(this.data.refreshInterval)
      this.setData({ 
        refreshInterval: null,
        autoRefresh: false
      })
      console.log('自动刷新已停止')
    }
  },

  /**
   * 切换详细信息显示
   */
  toggleDetails() {
    this.setData({
      showDetails: !this.data.showDetails
    })
  },

  /**
   * 选择函数查看详情
   */
  selectFunction(e) {
    const functionName = e.currentTarget.dataset.function
    this.setData({ selectedFunction: functionName })
  },

  /**
   * 重置统计数据
   */
  resetStats() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有统计数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          cloudFunctionManager.resetAllStats()
          this.loadMonitorData()
          wx.showToast({
            title: '重置成功',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 测试云函数调用
   */
  async testCloudFunction() {
    if (this.data.testing) return

    this.setData({ testing: true })

    try {
      wx.showLoading({ title: '测试中...' })

      // 执行简单的测试调用（只在调试页面输出）

      // 测试1：调用一个简单的云函数
      try {
        const testResult1 = await wx.cloud.callFunction({
          name: 'travel',
          data: { action: 'getTravelStatistics' }
        })
        // 测试1完成
      } catch (error) {
        // 测试1失败
      }

      // 测试2：测试缓存功能
      try {
        const testResult2 = await simpleDataManager.getData(
          'test_cache_key',
          async () => {
            return { success: true, data: { test: true, timestamp: Date.now() } }
          }
        )
        // 测试2完成
      } catch (error) {
        // 测试2失败
      }

      // 刷新监控数据
      await this.loadMonitorData()

      wx.showToast({
        title: '测试完成',
        icon: 'success'
      })

    } catch (error) {
      console.error('测试失败:', error)
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
      this.setData({ testing: false })
    }
  },

  /**
   * 清理缓存
   */
  clearCache() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有缓存吗？',
      success: (res) => {
        if (res.confirm) {
          // 清理简化数据管理器的缓存
          simpleDataManager.clearAllCaches()
          this.loadMonitorData()
          wx.showToast({
            title: '缓存已清理',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 导出监控数据
   */
  exportData() {
    try {
      const data = {
        timestamp: new Date().toISOString(),
        managerStats: this.data.managerStats,
        monitorReport: this.data.monitorReport
      }

      // 将数据转换为JSON字符串
      const jsonString = JSON.stringify(data, null, 2)
      
      // 复制到剪贴板
      wx.setClipboardData({
        data: jsonString,
        success: () => {
          wx.showToast({
            title: '数据已复制',
            icon: 'success'
          })
        }
      })

    } catch (error) {
      console.error('导出数据失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      })
    }
  },

  /**
   * 查看告警详情
   */
  viewAlert(e) {
    const alert = e.currentTarget.dataset.alert
    
    wx.showModal({
      title: `告警: ${alert.type}`,
      content: `${alert.message}\n\n时间: ${new Date(alert.timestamp).toLocaleString()}`,
      showCancel: false
    })
  },

  /**
   * 格式化数字
   */
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString()
  },

  /**
   * 获取告警级别样式
   */
  getAlertClass(type) {
    switch (type) {
      case 'HIGH_FREQUENCY':
      case 'HOURLY_LIMIT':
        return 'alert-danger'
      case 'HIGH_FAILURE_RATE':
        return 'alert-warning'
      case 'SLOW_RESPONSE':
        return 'alert-info'
      default:
        return 'alert-default'
    }
  }
})
