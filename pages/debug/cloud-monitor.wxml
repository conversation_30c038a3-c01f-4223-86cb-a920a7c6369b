<!--云函数调用监控页面-->
<view class="monitor-container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">云函数调用监控</text>
    <view class="header-actions">
      <button class="btn-refresh {{refreshing ? 'loading' : ''}}" 
              bindtap="onRefresh" disabled="{{refreshing}}">
        {{refreshing ? '刷新中...' : '刷新'}}
      </button>
      <button class="btn-auto {{autoRefresh ? 'active' : ''}}" 
              bindtap="toggleAutoRefresh">
        {{autoRefresh ? '停止自动' : '自动刷新'}}
      </button>
    </view>
  </view>

  <!-- 告警信息 -->
  <view class="alerts-section" wx:if="{{alerts.length > 0}}">
    <view class="section-title">
      <text>⚠️ 活跃告警 ({{alerts.length}})</text>
    </view>
    <view class="alerts-list">
      <view class="alert-item {{getAlertClass(item.type)}}" 
            wx:for="{{alerts}}" wx:key="id"
            bindtap="viewAlert" data-alert="{{item}}">
        <view class="alert-type">{{item.type}}</view>
        <view class="alert-message">{{item.message}}</view>
        <view class="alert-time">{{formatTime(item.timestamp)}}</view>
      </view>
    </view>
  </view>

  <!-- 概览统计 -->
  <view class="summary-section">
    <view class="section-title">
      <text>📊 调用概览</text>
    </view>
    <view class="summary-grid">
      <view class="summary-item">
        <view class="summary-value">{{managerStats.summary.totalCalls || 0}}</view>
        <view class="summary-label">总调用数</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{managerStats.summary.cacheHitRate || '0%'}}</view>
        <view class="summary-label">缓存命中率</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{managerStats.summary.blockRate || '0%'}}</view>
        <view class="summary-label">调用阻止率</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{monitorReport.averageResponseTime || '0ms'}}</view>
        <view class="summary-label">平均响应时间</view>
      </view>
    </view>
  </view>

  <!-- 详细统计 -->
  <view class="details-section">
    <view class="section-title" bindtap="toggleDetails">
      <text>📈 详细统计</text>
      <text class="toggle-icon">{{showDetails ? '▼' : '▶'}}</text>
    </view>
    
    <view class="details-content" wx:if="{{showDetails}}">
      <!-- 管理器统计 -->
      <view class="stats-group">
        <view class="stats-title">管理器统计</view>
        <view class="stats-list">
          <view class="stats-item">
            <text class="stats-label">总调用数:</text>
            <text class="stats-value">{{managerStats.manager.totalCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">缓存命中:</text>
            <text class="stats-value">{{managerStats.manager.cacheHits || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">被阻止调用:</text>
            <text class="stats-value">{{managerStats.manager.blockedCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">错误数:</text>
            <text class="stats-value">{{managerStats.manager.errors || 0}}</text>
          </view>
        </view>
      </view>

      <!-- 监控器统计 -->
      <view class="stats-group">
        <view class="stats-title">监控器统计</view>
        <view class="stats-list">
          <view class="stats-item">
            <text class="stats-label">成功调用:</text>
            <text class="stats-value">{{monitorReport.successCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">失败调用:</text>
            <text class="stats-value">{{monitorReport.failedCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">缓存调用:</text>
            <text class="stats-value">{{monitorReport.cachedCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">延迟调用:</text>
            <text class="stats-value">{{monitorReport.deferredCalls || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">每小时调用:</text>
            <text class="stats-value">{{monitorReport.callsPerHour || 0}}</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">运行时间:</text>
            <text class="stats-value">{{monitorReport.runningTime || '0秒'}}</text>
          </view>
        </view>
      </view>

      <!-- 函数统计 -->
      <view class="stats-group" wx:if="{{monitorReport.functionStats.length > 0}}">
        <view class="stats-title">函数调用统计</view>
        <view class="function-stats">
          <view class="function-item" 
                wx:for="{{monitorReport.functionStats}}" wx:key="function"
                bindtap="selectFunction" data-function="{{item.function}}">
            <view class="function-name">{{item.function}}</view>
            <view class="function-details">
              <text class="function-calls">{{item.calls}}次</text>
              <text class="function-success">成功率: {{item.successRate}}</text>
              <text class="function-time">响应: {{item.avgResponseTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <view class="section-title">
      <text>🛠️ 操作</text>
    </view>
    <view class="actions-grid">
      <button class="action-btn test" 
              bindtap="testCloudFunction" 
              disabled="{{testing}}">
        {{testing ? '测试中...' : '测试调用'}}
      </button>
      <button class="action-btn clear" bindtap="clearCache">
        清理缓存
      </button>
      <button class="action-btn reset" bindtap="resetStats">
        重置统计
      </button>
      <button class="action-btn export" bindtap="exportData">
        导出数据
      </button>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="footer-text">最后更新: {{formatTime(Date.now())}}</text>
    <text class="footer-text">监控状态: 运行中</text>
  </view>
</view>
