/**
 * 智谱AI服务
 * 处理智能文本解析和行程生成
 */

const fetch = require('node-fetch')

class ZhipuAIService {
  constructor(apiKey) {
    this.apiKey = apiKey
    this.baseURL = 'https://open.bigmodel.cn/api/paas/v4'
    this.model = 'glm-4-flash' // 免费模型
  }

  /**
   * 调用智谱AI API（带超时和重试机制）
   * @param {string} endpoint API端点
   * @param {object} data 请求数据
   * @param {number} timeout 超时时间（毫秒）
   * @param {number} retries 重试次数
   * @returns {object} API响应
   */
  async callAPI(endpoint, data, timeout = 5000, retries = 2) {
    let lastError

    for (let i = 0; i <= retries; i++) {
      try {
        console.log(`智谱AI API请求 (尝试 ${i + 1}/${retries + 1}):`, { endpoint, model: data.model })

        const response = await Promise.race([
          fetch(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
              'User-Agent': 'AI-Travel-MiniProgram/1.0'
            },
            body: JSON.stringify(data)
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('API调用超时')), timeout)
          )
        ])

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }

        const result = await response.json()

        // 检查API响应
        if (result.error) {
          throw new Error(`智谱AI错误: ${result.error.message}`)
        }

        return result
      } catch (error) {
        lastError = error
        console.error(`智谱AI API调用失败 (尝试 ${i + 1}):`, error)

        if (i < retries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }

    throw lastError
  }

  /**
   * 解析旅行攻略文本
   * @param {string} text 攻略文本
   * @returns {object} 解析结果
   */
  async parseTravel(text) {
    try {
      const prompt = `
请解析以下旅行攻略文本，提取关键信息并返回JSON格式的结构化数据。

要求：
1. 提取所有地点信息（景点、餐厅、酒店等）
2. 提取时间安排
3. 提取活动类型
4. 提取费用信息（如果有）
5. 提取实用建议

返回格式：
{
  "title": "行程标题",
  "duration": "行程天数",
  "destinations": [
    {
      "name": "地点名称",
      "type": "景点/餐厅/酒店/交通",
      "day": 1,
      "time": "09:00",
      "description": "描述",
      "estimatedDuration": 120,
      "estimatedCost": 50,
      "tips": ["建议1", "建议2"]
    }
  ],
  "totalEstimatedCost": 500,
  "tips": ["总体建议1", "总体建议2"]
}

攻略文本：
${text}
`

      const response = await this.callAPI('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      })

      const content = response.choices[0].message.content
      
      // 尝试解析JSON（标准化错误处理）
      try {
        const cleanContent = content
          .replace(/```json\n?/g, '')
          .replace(/```\n?/g, '')
          .trim()

        const parsed = JSON.parse(cleanContent)
        return {
          success: true,
          data: parsed,
          message: '解析成功'
        }
      } catch (parseError) {
        // JSON解析失败，使用降级策略
        console.warn('JSON解析失败，使用降级策略:', parseError)
        return {
          success: false,
          message: 'JSON解析失败，但获取到原始内容',
          data: {
            title: '解析结果',
            rawText: content,
            parseError: parseError.message
          },
          error: parseError.message
        }
      }
    } catch (error) {
      console.error('解析旅行攻略失败:', error)
      throw new Error(`解析旅行攻略失败: ${error.message}`)
    }
  }

  /**
   * 智能生成行程
   * @param {object} requirements 用户需求
   * @returns {object} 生成的行程
   */
  async generateItinerary(requirements) {
    try {
      const {
        destination,
        duration,
        budget,
        interests,
        travelStyle,
        groupSize,
        startDate
      } = requirements

      const prompt = `
请根据以下需求生成详细的旅行行程：

目的地：${destination}
行程天数：${duration}天
预算：${budget || '不限'}
兴趣偏好：${interests || '常规景点'}
旅行风格：${travelStyle || '休闲'}
人数：${groupSize || 1}人
出发日期：${startDate || '待定'}

要求：
1. 合理安排每天的行程
2. 考虑地理位置，优化路线
3. 包含景点、餐饮、住宿建议
4. 提供预估费用
5. 给出实用建议

返回JSON格式：
{
  "title": "行程标题",
  "summary": "行程概述",
  "totalDays": ${duration},
  "estimatedBudget": {
    "total": 2000,
    "breakdown": {
      "accommodation": 800,
      "food": 600,
      "transportation": 400,
      "attractions": 200
    }
  },
  "itinerary": [
    {
      "day": 1,
      "title": "第一天标题",
      "items": [
        {
          "time": "09:00",
          "title": "活动名称",
          "type": "景点/餐厅/交通",
          "description": "详细描述",
          "estimatedDuration": 120,
          "estimatedCost": 50,
          "tips": ["建议1"]
        }
      ]
    }
  ],
  "generalTips": ["总体建议1", "总体建议2"],
  "packingList": ["必带物品1", "必带物品2"]
}
`

      const response = await this.callAPI('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 3000
      })

      const content = response.choices[0].message.content
      
      try {
        const parsed = JSON.parse(content)
        return {
          success: true,
          data: parsed,
          requirements: requirements,
          generatedAt: new Date().toISOString()
        }
      } catch (parseError) {
        console.warn('行程JSON解析失败:', parseError)
        return {
          success: true,
          data: {
            title: '智能生成行程',
            rawText: content,
            parseError: parseError.message
          },
          requirements: requirements
        }
      }
    } catch (error) {
      console.error('生成行程失败:', error)
      throw new Error(`生成行程失败: ${error.message}`)
    }
  }

  /**
   * 优化行程建议
   * @param {object} currentItinerary 当前行程
   * @returns {object} 优化建议
   */
  async optimizeItinerary(currentItinerary) {
    try {
      const prompt = `
请分析以下行程并提供优化建议：

${JSON.stringify(currentItinerary, null, 2)}

请从以下角度提供建议：
1. 路线优化（减少往返）
2. 时间安排优化
3. 费用优化
4. 体验优化

返回JSON格式的优化建议。
`

      const response = await this.callAPI('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        max_tokens: 2000
      })

      const content = response.choices[0].message.content
      
      try {
        const parsed = JSON.parse(content)
        return {
          success: true,
          data: parsed,
          originalItinerary: currentItinerary
        }
      } catch (parseError) {
        return {
          success: true,
          data: {
            suggestions: content,
            parseError: parseError.message
          }
        }
      }
    } catch (error) {
      console.error('优化行程失败:', error)
      throw new Error(`优化行程失败: ${error.message}`)
    }
  }

  /**
   * 测试连接
   * @returns {boolean} 连接是否成功
   */
  async testConnection() {
    try {
      const response = await this.callAPI('/chat/completions', {
        model: this.model,
        messages: [
          {
            role: 'user',
            content: '你好，请回复"连接成功"'
          }
        ],
        max_tokens: 10
      })

      return response.choices && response.choices.length > 0
    } catch (error) {
      // 只在开发环境输出详细错误
      if (process.env.NODE_ENV === 'development') {
        console.error('智谱AI连接测试失败:', error)
      }
      throw error
    }
  }

  /**
   * 获取模型信息
   * @returns {object} 模型信息
   */
  getModelInfo() {
    return {
      model: this.model,
      provider: '智谱AI',
      features: [
        '文本解析',
        '行程生成',
        '智能优化',
        '多轮对话'
      ],
      limits: {
        maxTokens: 4000,
        temperature: '0.1-1.0',
        freeUsage: '无限制'
      }
    }
  }
}

module.exports = ZhipuAIService
