/**
 * 和风天气服务
 * 使用JWT认证方式调用和风天气API
 */

const fetch = require('node-fetch')
const { createJWT } = require('../utils/jwt-helper')

class QWeatherService {
  constructor(keyId, projectId, privateKey) {
    this.keyId = keyId
    this.projectId = projectId
    this.privateKey = privateKey
    this.baseURL = 'https://mv4gkhgvd4.re.qweatherapi.com'
    
    // 如果配置完整，创建JWT实例
    if (keyId && projectId && privateKey) {
      this.jwt = createJWT(keyId, projectId, privateKey)
    } else {
      // 只在开发环境输出配置警告
      if (process.env.NODE_ENV === 'development') {
        console.warn('和风天气JWT配置不完整，相关功能将不可用')
      }
    }
  }

  /**
   * 生成认证头
   * @returns {object} 请求头
   */
  getAuthHeaders() {
    if (!this.jwt) {
      throw new Error('和风天气JWT未配置')
    }

    const token = this.jwt.generateToken()
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'User-Agent': 'AI-Travel-MiniProgram/1.0'
    }
  }

  /**
   * 发送API请求
   * @param {string} endpoint API端点
   * @param {object} params 请求参数
   * @returns {object} API响应
   */
  async request(endpoint, params = {}) {
    try {
      const headers = this.getAuthHeaders()
      
      // 构建查询参数
      const queryParams = new URLSearchParams(params)
      const url = `${this.baseURL}${endpoint}?${queryParams}`
      
      console.log('和风天气API请求:', { endpoint, params })
      
      const response = await fetch(url, {
        method: 'GET',
        headers: headers
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      // 检查API响应状态
      if (data.code !== '200') {
        throw new Error(`和风天气API错误: ${data.code} - ${this.getErrorMessage(data.code)}`)
      }

      return data
    } catch (error) {
      console.error('和风天气API请求失败:', error)
      throw error
    }
  }

  /**
   * 获取实时天气
   * @param {string} location 地点（经纬度或城市ID）
   * @returns {object} 天气信息
   */
  async getCurrentWeather(location) {
    try {
      const data = await this.request('/v7/weather/now', { location })
      
      return {
        location: location,
        weather: data.now,
        updateTime: data.updateTime,
        fxLink: data.fxLink
      }
    } catch (error) {
      console.error('获取实时天气失败:', error)
      throw new Error(`获取实时天气失败: ${error.message}`)
    }
  }

  /**
   * 获取天气预报
   * @param {string} location 地点
   * @param {number} days 预报天数（3, 7, 10, 15）
   * @returns {object} 天气预报
   */
  async getWeatherForecast(location, days = 3) {
    try {
      const endpoint = `/v7/weather/${days}d`
      const data = await this.request(endpoint, { location })
      
      return {
        location: location,
        forecast: data.daily,
        updateTime: data.updateTime,
        fxLink: data.fxLink
      }
    } catch (error) {
      console.error('获取天气预报失败:', error)
      throw new Error(`获取天气预报失败: ${error.message}`)
    }
  }

  /**
   * 获取天气指数
   * @param {string} location 地点
   * @param {string} type 指数类型（可选：1,2,3,5,6,8,9）
   * @returns {object} 天气指数
   */
  async getWeatherIndices(location, type = '1,2,3') {
    try {
      const data = await this.request('/v7/indices/1d', { 
        location, 
        type 
      })
      
      return {
        location: location,
        indices: data.daily,
        updateTime: data.updateTime
      }
    } catch (error) {
      console.error('获取天气指数失败:', error)
      throw new Error(`获取天气指数失败: ${error.message}`)
    }
  }

  /**
   * 城市搜索
   * @param {string} location 城市名称
   * @returns {object} 城市信息
   */
  async searchCity(location) {
    try {
      const data = await this.request('/v2/city/lookup', { location })
      
      return {
        query: location,
        cities: data.location || []
      }
    } catch (error) {
      console.error('城市搜索失败:', error)
      throw new Error(`城市搜索失败: ${error.message}`)
    }
  }

  /**
   * 获取综合天气信息（用于旅行规划）
   * @param {string} location 地点
   * @param {string} date 日期（可选）
   * @returns {object} 综合天气信息
   */
  async getWeather(location, date = null) {
    try {
      // 获取实时天气
      const current = await this.getCurrentWeather(location)
      
      // 获取天气预报
      const forecast = await this.getWeatherForecast(location, 7)
      
      // 获取生活指数
      const indices = await this.getWeatherIndices(location)
      
      // 如果指定了日期，筛选对应日期的预报
      let targetForecast = null
      if (date && forecast.forecast) {
        targetForecast = forecast.forecast.find(day => day.fxDate === date)
      }
      
      return {
        location: location,
        current: current.weather,
        forecast: targetForecast || forecast.forecast[0], // 如果没找到指定日期，返回今天的
        indices: indices.indices,
        updateTime: current.updateTime,
        requestTime: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取综合天气信息失败:', error)
      throw new Error(`获取天气信息失败: ${error.message}`)
    }
  }

  /**
   * 测试连接
   * @returns {boolean} 连接是否成功
   */
  async testConnection() {
    try {
      // 使用北京的城市ID测试连接
      await this.getCurrentWeather('101010100')
      return true
    } catch (error) {
      console.error('和风天气连接测试失败:', error)
      throw error
    }
  }

  /**
   * 获取错误信息
   * @param {string} code 错误码
   * @returns {string} 错误信息
   */
  getErrorMessage(code) {
    const errorMessages = {
      '400': '请求错误，可能包含错误的请求参数或缺少必需的请求参数',
      '401': '认证失败，可能使用了错误的KEY、数字签名错误、KEY的类型错误',
      '402': '超过访问次数或余额不足以支持继续访问服务',
      '403': '无访问权限，可能是绑定的PackageName、BundleID、域名IP地址不一致',
      '404': '查询的数据或地区不存在',
      '429': '超过限定的QPM',
      '500': '无响应或超时，接口服务异常'
    }
    
    return errorMessages[code] || `未知错误码: ${code}`
  }

  /**
   * 获取配置状态
   * @returns {object} 配置状态
   */
  getConfigStatus() {
    return {
      configured: !!(this.keyId && this.projectId && this.privateKey),
      keyId: this.keyId ? '已配置' : '未配置',
      projectId: this.projectId ? '已配置' : '未配置',
      privateKey: this.privateKey ? '已配置' : '未配置'
    }
  }
}

module.exports = QWeatherService
