// cloud/functions/user/index.js
// 用户数据相关云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { action, data, options } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'getUserInfo':
        return await getUserInfo(OPENID)
      case 'updateUserInfo':
        return await updateUserInfo(OPENID, data)
      case 'getUserStats':
        return await getUserStats(OPENID, data)
      case 'getUserProfile':
        return await getUserProfile(OPENID)
      case 'updateUserProfile':
        return await updateUserProfile(OPENID, data)
      case 'getUserPreferences':
        return await getUserPreferences(OPENID)
      case 'updateUserPreferences':
        return await updateUserPreferences(OPENID, data)
      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('User云函数错误:', error)
    return {
      success: false,
      message: error.message || '服务器错误'
    }
  }
}

// 获取用户基本信息
async function getUserInfo(openid) {
  try {
    const result = await db.collection('users')
      .where({ _openid: openid })
      .get()

    if (result.data.length > 0) {
      const userData = result.data[0]
      return {
        success: true,
        data: {
          openid: userData._openid,
          nickName: userData.nickName || '微信用户',
          avatarUrl: userData.avatarUrl || '/images/user.svg',
          createTime: userData.createTime,
          updateTime: userData.updateTime
        }
      }
    } else {
      // 创建新用户
      const newUser = {
        nickName: '微信用户',
        avatarUrl: '/images/user.svg',
        createTime: new Date(),
        updateTime: new Date()
      }

      await db.collection('users').add({
        data: newUser
      })

      return {
        success: true,
        data: {
          openid,
          ...newUser
        }
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      message: '获取用户信息失败'
    }
  }
}

// 更新用户信息
async function updateUserInfo(openid, userData) {
  try {
    const updateData = {
      ...userData,
      updateTime: new Date()
    }

    await db.collection('users')
      .where({ _openid: openid })
      .update({
        data: updateData
      })

    return {
      success: true,
      message: '用户信息更新成功'
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    return {
      success: false,
      message: '更新用户信息失败'
    }
  }
}

// 获取用户统计数据
async function getUserStats(openid, options = {}) {
  try {
    console.log('获取用户统计数据，用户:', openid)
    
    // 并行获取各种数据
    const [financialResult, travelResult, socialResult] = await Promise.allSettled([
      getFinancialStats(openid),
      getTravelStats(openid),
      getSocialStats(openid)
    ])
    
    const stats = {
      financial: financialResult.status === 'fulfilled' ? financialResult.value : getDefaultFinancialStats(),
      travel: travelResult.status === 'fulfilled' ? travelResult.value : getDefaultTravelStats(),
      social: socialResult.status === 'fulfilled' ? socialResult.value : getDefaultSocialStats(),
      lastUpdated: new Date()
    }
    
    console.log('用户统计结果:', stats)
    
    return {
      success: true,
      data: stats,
      version: Date.now()
    }
    
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      success: false,
      message: error.message || '获取用户统计失败'
    }
  }
}

// 获取用户参与的所有协作计划（包括创建的和参与的）
async function getUserCollaborationPlans(openid) {
  // 获取用户创建的协作计划
  const createdPlans = await db.collection('travel_plans')
    .where({
      _openid: openid,
      'collaboration.enabled': true
    })
    .get()

  // 获取用户参与的协作计划
  const joinedPlans = await db.collection('travel_plans')
    .where({
      'collaboration.enabled': true,
      'collaboration.collaborators.openid': openid,
      _openid: _.neq(openid)
    })
    .get()

  // 合并并去重
  const allPlans = [...createdPlans.data, ...joinedPlans.data]
  const uniquePlans = []
  const planIds = new Set()

  allPlans.forEach(plan => {
    if (!planIds.has(plan._id)) {
      uniquePlans.push(plan)
      planIds.add(plan._id)
    }
  })

  return uniquePlans
}

// 获取财务统计
async function getFinancialStats(openid) {
  try {
    // 获取本月支出记录
    const thisMonth = getTimeRange('month')

    // 获取用户参与的所有协作计划
    const collaborationPlans = await getUserCollaborationPlans(openid)
    const collaboratedPlanIds = collaborationPlans.map(plan => plan._id)

    // 获取个人支出（非协作，本月）
    const personalExpenseResult = await db.collection('expense_records')
      .where({
        _openid: openid,
        travel_plan_id: null,
        createTime: _.gte(thisMonth.start).and(_.lte(thisMonth.end))
      })
      .get()

    // 获取协作支出（本月）
    let collaboratedExpenseResult = { data: [] }
    if (collaboratedPlanIds.length > 0) {
      collaboratedExpenseResult = await db.collection('expense_records')
        .where({
          travel_plan_id: _.in(collaboratedPlanIds),
          type: 'travel',
          createTime: _.gte(thisMonth.start).and(_.lte(thisMonth.end))
        })
        .get()
    }

    // 合并并去重（使用Map按_id去重）
    const allExpenses = [...personalExpenseResult.data, ...collaboratedExpenseResult.data]
    const expenses = Array.from(
      new Map(allExpenses.map(record => [record._id, record])).values()
    )
    const totalExpense = expenses.reduce((sum, record) => sum + (record.amount || 0), 0)
    const recordCount = expenses.length
    
    // 获取预算信息
    const budgetResult = await db.collection('user_budgets')
      .where({ _openid: openid })
      .get()
    
    const budget = budgetResult.data.length > 0 ? budgetResult.data[0] : { monthly: 5000 }
    const budgetUsage = budget.monthly > 0 ? (totalExpense / budget.monthly) * 100 : 0
    
    return {
      totalExpense: Math.round(totalExpense * 100) / 100,
      recordCount,
      budgetUsage: Math.round(budgetUsage * 100) / 100,
      avgDailyExpense: Math.round((totalExpense / 30) * 100) / 100
    }
    
  } catch (error) {
    // 只在开发环境输出详细错误
    if (process.env.NODE_ENV === 'development') {
      console.error('获取财务统计失败:', error)
    }
    return getDefaultFinancialStats()
  }
}

// 获取旅行统计
async function getTravelStats(openid) {
  try {
    // {{ AURA-X: Modify - 支持协作计划的统计数据. Approval: 寸止(ID:1738056000). }}
    // 获取用户创建的旅行计划
    const createdPlansResult = await db.collection('travel_plans')
      .where({ _openid: openid })
      .get()

    // 获取用户作为协作者参与的旅行计划
    const collaboratedPlansResult = await db.collection('travel_plans')
      .where({
        'collaboration.enabled': true,
        'collaboration.collaborators.openid': openid
      })
      .get()

    // 合并计划并去重
    const allPlans = []
    const planIds = new Set()

    // 添加创建的计划
    createdPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push(plan)
        planIds.add(plan._id)
      }
    })

    // 添加协作的计划
    collaboratedPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push(plan)
        planIds.add(plan._id)
      }
    })

    const plans = allPlans
    const totalPlans = plans.length
    const completedPlans = plans.filter(p => p.status === 'completed').length
    const ongoingPlans = plans.filter(p => p.status === 'ongoing').length

    // {{ AURA-X: Modify - 修复数据库集合和字段名. Approval: 寸止(ID:1738056000). }}
    // 获取所有相关计划的旅行支出
    let travelExpenses = []
    if (planIds.size > 0) {
      const travelExpenseResult = await db.collection('expense_records')
        .where({
          type: 'travel',
          travel_plan_id: _.in([...planIds])
        })
        .get()
      travelExpenses = travelExpenseResult.data || []
    }

    const totalTravelExpense = travelExpenses.reduce((sum, record) => sum + (record.amount || 0), 0)
    
    return {
      totalPlans,
      completedPlans,
      ongoingPlans,
      totalTravelExpense: Math.round(totalTravelExpense * 100) / 100,
      avgExpensePerTrip: completedPlans > 0 ? Math.round((totalTravelExpense / completedPlans) * 100) / 100 : 0
    }
    
  } catch (error) {
    console.error('获取旅行统计失败:', error)
    return getDefaultTravelStats()
  }
}

// 获取社交统计
async function getSocialStats(openid) {
  try {
    // 这里可以扩展社交功能的统计
    // 目前返回默认值
    return {
      friendCount: 0,
      shareCount: 0,
      likeCount: 0
    }
    
  } catch (error) {
    console.error('获取社交统计失败:', error)
    return getDefaultSocialStats()
  }
}

// 获取用户资料
async function getUserProfile(openid) {
  try {
    const result = await db.collection('user_profiles')
      .where({ _openid: openid })
      .get()
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      return {
        success: true,
        data: getDefaultUserProfile()
      }
    }
    
  } catch (error) {
    console.error('获取用户资料失败:', error)
    return {
      success: false,
      message: error.message || '获取用户资料失败'
    }
  }
}

// 更新用户资料
async function updateUserProfile(openid, profileData) {
  try {
    const data = {
      ...profileData,
      _openid: openid,
      updateTime: new Date()
    }
    
    const existingProfile = await db.collection('user_profiles')
      .where({ _openid: openid })
      .get()
    
    let result
    if (existingProfile.data && existingProfile.data.length > 0) {
      result = await db.collection('user_profiles')
        .where({ _openid: openid })
        .update({ data })
    } else {
      data.createTime = new Date()
      result = await db.collection('user_profiles').add({ data })
    }
    
    return {
      success: true,
      data: profileData,
      message: '用户资料更新成功'
    }
    
  } catch (error) {
    console.error('更新用户资料失败:', error)
    return {
      success: false,
      message: error.message || '更新用户资料失败'
    }
  }
}

// 获取用户偏好设置
async function getUserPreferences(openid) {
  try {
    const result = await db.collection('user_preferences')
      .where({ _openid: openid })
      .get()
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      return {
        success: true,
        data: getDefaultUserPreferences()
      }
    }
    
  } catch (error) {
    console.error('获取用户偏好失败:', error)
    return {
      success: false,
      message: error.message || '获取用户偏好失败'
    }
  }
}

// 更新用户偏好设置
async function updateUserPreferences(openid, preferencesData) {
  try {
    const data = {
      ...preferencesData,
      _openid: openid,
      updateTime: new Date()
    }
    
    const existingPreferences = await db.collection('user_preferences')
      .where({ _openid: openid })
      .get()
    
    let result
    if (existingPreferences.data && existingPreferences.data.length > 0) {
      result = await db.collection('user_preferences')
        .where({ _openid: openid })
        .update({ data })
    } else {
      data.createTime = new Date()
      result = await db.collection('user_preferences').add({ data })
    }
    
    return {
      success: true,
      data: preferencesData,
      message: '用户偏好更新成功'
    }
    
  } catch (error) {
    console.error('更新用户偏好失败:', error)
    return {
      success: false,
      message: error.message || '更新用户偏好失败'
    }
  }
}

// 默认数据函数
function getDefaultFinancialStats() {
  return {
    totalExpense: 0,
    recordCount: 0,
    budgetUsage: 0,
    avgDailyExpense: 0
  }
}

function getDefaultTravelStats() {
  return {
    totalPlans: 0,
    completedPlans: 0,
    ongoingPlans: 0,
    totalTravelExpense: 0,
    avgExpensePerTrip: 0
  }
}

function getDefaultSocialStats() {
  return {
    friendCount: 0,
    shareCount: 0,
    likeCount: 0
  }
}

function getDefaultUserProfile() {
  return {
    nickname: '',
    avatar: '',
    bio: '',
    location: ''
  }
}

function getDefaultUserPreferences() {
  return {
    theme: 'auto',
    notifications: true,
    privacy: 'normal',
    language: 'zh-CN'
  }
}

// 工具函数：获取时间范围
function getTimeRange(period) {
  const now = new Date()
  const start = new Date()
  
  switch (period) {
    case 'week':
      start.setDate(now.getDate() - 7)
      break
    case 'month':
      start.setMonth(now.getMonth() - 1)
      break
    case 'quarter':
      start.setMonth(now.getMonth() - 3)
      break
    case 'year':
      start.setFullYear(now.getFullYear() - 1)
      break
    default:
      start.setMonth(now.getMonth() - 1)
  }
  
  return {
    start: start,
    end: now
  }
}
