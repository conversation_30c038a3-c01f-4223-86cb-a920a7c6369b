// 目的地管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const startTime = Date.now()
  const { action, data } = event
  const wxContext = cloud.getWXContext()

  // 只在开发环境记录请求日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${new Date().toISOString()}] destinations云函数调用`, {
      action,
      openid: wxContext.OPENID,
      data: JSON.stringify(data)
    })
  }

  try {
    let result

    switch (action) {
      case 'searchDestinations':
        result = await searchDestinations(data)
        break
      case 'getCategoryDestinations':
        result = await getCategoryDestinations(data)
        break
      case 'getDestinationDetail':
        result = await getDestinationDetail(data)
        break
      case 'addDestinationVisit':
        result = await addDestinationVisit(data, wxContext.OPENID)
        break
      case 'addDestination':
        result = await addDestination(data, wxContext.OPENID)
        break
      default:
        console.error(`无效的操作类型: ${action}`)
        return {
          success: false,
          error: 'INVALID_ACTION',
          message: `无效的操作类型: ${action}`
        }
    }

    // 记录成功日志
    const duration = Date.now() - startTime
    console.log(`[${new Date().toISOString()}] destinations云函数执行成功`, {
      action,
      duration: `${duration}ms`,
      success: result.success
    })

    return result

  } catch (error) {
    // 记录错误日志
    const duration = Date.now() - startTime
    console.error(`[${new Date().toISOString()}] destinations云函数执行失败`, {
      action,
      duration: `${duration}ms`,
      error: error.message,
      stack: error.stack
    })

    return {
      success: false,
      error: error.code || 'UNKNOWN_ERROR',
      message: error.message || '操作失败，请重试',
      timestamp: new Date().toISOString()
    }
  }
}

// 热门目的地功能已移除，将在发现页面中实现

// 搜索目的地
async function searchDestinations(data) {
  try {
    const { keyword, limit = 20 } = data

    // 参数验证
    if (!keyword || typeof keyword !== 'string') {
      return {
        success: true,
        data: [],
        total: 0,
        message: '搜索关键词为空'
      }
    }

    const trimmedKeyword = keyword.trim()
    if (trimmedKeyword.length === 0) {
      return {
        success: true,
        data: [],
        total: 0,
        message: '搜索关键词为空'
      }
    }

    if (trimmedKeyword.length > 50) {
      throw new Error('搜索关键词过长')
    }

    // 使用正则表达式进行模糊搜索
    const result = await db.collection('destinations')
      .where({
        status: 'active',
        name: db.RegExp({
          regexp: trimmedKeyword,
          options: 'i'
        })
      })
      .limit(Math.min(limit, 50))
      .get()

    console.log(`搜索目的地"${trimmedKeyword}"成功，返回${result.data.length}条记录`)

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      keyword: trimmedKeyword,
      message: '搜索目的地成功'
    }

  } catch (error) {
    console.error('搜索目的地失败:', error)
    throw new Error(`搜索目的地失败: ${error.message}`)
  }
}

// 获取分类目的地
async function getCategoryDestinations(data) {
  const { category, limit = 50 } = data
  
  let whereCondition = { status: 'active' }
  
  switch (category) {
    case 'domestic':
      whereCondition.country = '中国'
      break
    case 'international':
      whereCondition.country = _.neq('中国')
      break
    case 'nearby':
      whereCondition.isNearby = true
      break
    // 热门分类已移除，将在发现页面中实现
  }

  const result = await db.collection('destinations')
    .where(whereCondition)
    .orderBy('visitCount', 'desc')
    .limit(limit)
    .get()

  return {
    success: true,
    data: result.data,
    message: '获取分类目的地成功'
  }
}

// 获取目的地详情
async function getDestinationDetail(data) {
  const { destinationId } = data
  
  if (!destinationId) {
    return {
      success: false,
      error: 'MISSING_DESTINATION_ID',
      message: '目的地ID不能为空'
    }
  }

  const result = await db.collection('destinations')
    .doc(destinationId)
    .get()

  if (!result.data) {
    return {
      success: false,
      error: 'DESTINATION_NOT_FOUND',
      message: '目的地不存在'
    }
  }

  return {
    success: true,
    data: result.data,
    message: '获取目的地详情成功'
  }
}

// 添加目的地访问记录
async function addDestinationVisit(data, openid) {
  const { destinationId } = data
  
  if (!destinationId) {
    return {
      success: false,
      error: 'MISSING_DESTINATION_ID',
      message: '目的地ID不能为空'
    }
  }

  // 增加访问计数
  await db.collection('destinations')
    .doc(destinationId)
    .update({
      data: {
        visitCount: _.inc(1),
        lastVisitTime: new Date()
      }
    })

  // 记录用户访问历史
  await db.collection('destination_visits')
    .add({
      data: {
        destinationId,
        openid,
        visitTime: new Date(),
        createTime: new Date()
      }
    })

  return {
    success: true,
    message: '访问记录添加成功'
  }
}

// 添加新目的地（管理员功能）
async function addDestination(data, openid) {
  const {
    name,
    province,
    country,
    description,
    coordinates,
    tags,
    images
  } = data

  if (!name || !province || !country) {
    return {
      success: false,
      error: 'MISSING_REQUIRED_FIELDS',
      message: '目的地名称、省份、国家不能为空'
    }
  }

  const destinationData = {
    name,
    province,
    country,
    description: description || '',
    coordinates: coordinates || null,
    tags: tags || [],
    images: images || [],
    isHot: false,
    isNearby: false,
    status: 'active',
    visitCount: 0,
    rating: 0,
    reviewCount: 0,
    createdBy: openid,
    createTime: new Date(),
    updateTime: new Date()
  }

  const result = await db.collection('destinations')
    .add({
      data: destinationData
    })

  return {
    success: true,
    data: {
      _id: result._id,
      ...destinationData
    },
    message: '目的地添加成功'
  }
}
