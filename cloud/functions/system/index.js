// cloud/functions/system/index.js
// 系统管理云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, collections } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'getCollections':
        return await getCollections()
      case 'createCollections':
        return await createCollections(collections)
      case 'initializeDatabase':
        return await initializeDatabase()
      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    // 只在开发环境输出详细错误
    if (process.env.NODE_ENV === 'development') {
      console.error('系统云函数错误:', error)
    }
    return {
      success: false,
      message: error.message || '服务器错误'
    }
  }
}

// 获取数据库集合列表
async function getCollections() {
  try {
    // 尝试查询各个集合来检查是否存在
    const collections = []
    const collectionNames = ['users', 'expense_records', 'travel_plans', 'categories', 'destinations']
    
    for (const name of collectionNames) {
      try {
        await db.collection(name).limit(1).get()
        collections.push({ name, exists: true })
      } catch (error) {
        collections.push({ name, exists: false })
      }
    }
    
    return {
      success: true,
      collections: collections.filter(col => col.exists)
    }
  } catch (error) {
    console.error('获取集合列表失败:', error)
    return {
      success: false,
      message: error.message || '获取集合列表失败'
    }
  }
}

// 创建数据库集合
async function createCollections(collectionNames) {
  try {
    const results = []
    
    for (const name of collectionNames) {
      try {
        // 尝试创建集合（通过添加一个临时文档然后删除）
        const tempDoc = await db.collection(name).add({
          data: {
            _temp: true,
            createTime: new Date()
          }
        })
        
        // 删除临时文档
        await db.collection(name).doc(tempDoc._id).remove()
        
        results.push({ name, success: true })
        console.log(`集合 ${name} 创建成功`)
      } catch (error) {
        results.push({ name, success: false, error: error.message })
        console.error(`集合 ${name} 创建失败:`, error)
      }
    }
    
    return {
      success: true,
      results: results
    }
  } catch (error) {
    console.error('创建集合失败:', error)
    return {
      success: false,
      message: error.message || '创建集合失败'
    }
  }
}

// 初始化数据库
async function initializeDatabase() {
  try {
    console.log('开始初始化数据库...')
    
    // 创建所有必要的集合
    const collectionNames = ['users', 'records', 'travel_plans', 'categories', 'destinations']
    const createResult = await createCollections(collectionNames)
    
    // 初始化默认分类数据
    await initializeDefaultCategories()
    
    console.log('数据库初始化完成')
    
    return {
      success: true,
      message: '数据库初始化完成',
      createResult: createResult
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: error.message || '数据库初始化失败'
    }
  }
}

// 初始化默认分类数据
async function initializeDefaultCategories() {
  try {
    // 检查是否已有分类数据
    const existingCategories = await db.collection('categories').count()
    
    if (existingCategories.total > 0) {
      console.log('分类数据已存在，跳过初始化')
      return
    }
    
    const defaultCategories = [
      {
        name: '餐饮',
        icon: '🍽️',
        type: 'expense',
        color: '#FF6B6B',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '交通',
        icon: '🚗',
        type: 'expense',
        color: '#4ECDC4',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '购物',
        icon: '🛍️',
        type: 'expense',
        color: '#45B7D1',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '娱乐',
        icon: '🎮',
        type: 'expense',
        color: '#96CEB4',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '住宿',
        icon: '🏨',
        type: 'expense',
        color: '#FF9F43',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '工资',
        icon: '💰',
        type: 'income',
        color: '#FFEAA7',
        isDefault: true,
        createTime: new Date()
      },
      {
        name: '奖金',
        icon: '🎁',
        type: 'income',
        color: '#DDA0DD',
        isDefault: true,
        createTime: new Date()
      }
    ]
    
    // 批量添加默认分类
    for (const category of defaultCategories) {
      await db.collection('categories').add({
        data: category
      })
    }
    
    console.log('默认分类数据初始化完成')
  } catch (error) {
    console.error('初始化默认分类数据失败:', error)
    throw error
  }
}
