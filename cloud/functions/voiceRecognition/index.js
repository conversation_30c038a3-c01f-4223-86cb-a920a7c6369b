// 语音识别云函数
const cloud = require('wx-server-sdk')
const tencentcloud = require('tencentcloud-sdk-nodejs')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 腾讯云语音识别客户端
const AsrClient = tencentcloud.asr.v20190614.Client

// 腾讯云配置
const clientConfig = {
  credential: {
    secretId: 'AKIDxjITdaN1UkqnZ8UksnV4TlspbDh4IMDi',
    secretKey: 'MabO1Vnv6ECyeJ2rZ6QXffT6YqzxvmTz',
  },
  region: 'ap-beijing',
  profile: {
    httpProfile: {
      endpoint: 'asr.tencentcloudapi.com',
    },
  },
}

const client = new AsrClient(clientConfig)

exports.main = async (event, context) => {
  const { action, data } = event
  const wxContext = cloud.getWXContext()

  try {
    switch (action) {
      case 'recognizeVoice':
        return await recognizeVoice(data, wxContext.OPENID)
      case 'saveVoiceRecord':
        return await saveVoiceRecord(data, wxContext.OPENID)
      case 'getVoiceHistory':
        return await getVoiceHistory(data, wxContext.OPENID)
      default:
        return {
          success: false,
          error: 'INVALID_ACTION',
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    // 只在开发环境输出详细错误
    if (process.env.NODE_ENV === 'development') {
      console.error('语音识别云函数错误:', error)
    }
    return {
      success: false,
      error: error.message || 'UNKNOWN_ERROR',
      message: '操作失败，请重试'
    }
  }
}

// 语音识别处理
async function recognizeVoice(data, openid) {
  const { fileID, duration, format = 'mp3' } = data

  if (!fileID) {
    return {
      success: false,
      error: 'MISSING_FILE_ID',
      message: '音频文件ID不能为空'
    }
  }

  try {
    console.log(`开始处理语音识别，fileID: ${fileID}`)

    // 1. 下载音频文件
    const audioBuffer = await downloadAudioFile(fileID)

    // 2. 调用腾讯云语音识别API
    const recognitionResult = await callTencentASR(audioBuffer, format, duration)

    // 3. 解析识别结果
    const parsedData = parseVoiceText(recognitionResult.text)

    // 4. 保存识别记录
    await saveRecognitionLog(openid, fileID, recognitionResult.text, parsedData)

    return {
      success: true,
      data: {
        originalText: recognitionResult.text,
        confidence: recognitionResult.confidence,
        parsedData: parsedData,
        fileID: fileID,
        duration: duration,
        timestamp: new Date().toISOString()
      },
      message: '语音识别成功'
    }

  } catch (error) {
    console.error('语音识别失败:', error)

    // 如果是网络错误或API错误，提供降级方案
    if (error.code === 'NETWORK_ERROR' || error.code === 'API_ERROR') {
      return await fallbackRecognition(data, openid)
    }

    return {
      success: false,
      error: 'RECOGNITION_FAILED',
      message: `语音识别失败: ${error.message}`
    }
  }
}

// 下载音频文件
async function downloadAudioFile(fileID) {
  try {
    const result = await cloud.downloadFile({
      fileID: fileID
    })

    if (!result.fileContent) {
      throw new Error('音频文件下载失败')
    }

    return result.fileContent
  } catch (error) {
    console.error('下载音频文件失败:', error)
    throw new Error(`音频文件下载失败: ${error.message}`)
  }
}

// 调用腾讯云语音识别API
async function callTencentASR(audioBuffer, format, duration) {
  try {
    // 将音频转换为Base64
    const audioBase64 = audioBuffer.toString('base64')

    const params = {
      EngineModelType: '16k_zh',
      ChannelNum: 1,
      ResTextFormat: 0,
      SourceType: 1,
      Data: audioBase64,
      DataLen: audioBuffer.length,
      VoiceFormat: format === 'mp3' ? 'mp3' : 'wav'
    }

    const response = await client.SentenceRecognition(params)

    if (response.Result && response.Result.length > 0) {
      return {
        text: response.Result,
        confidence: 0.9 // 腾讯云API通常不返回置信度，使用默认值
      }
    } else {
      throw new Error('语音识别结果为空')
    }

  } catch (error) {
    console.error('腾讯云ASR调用失败:', error)
    throw new Error(`语音识别API调用失败: ${error.message}`)
  }
}

// 降级识别方案
async function fallbackRecognition(data, openid) {
  console.log('使用降级识别方案')

  // 使用简单的模式匹配或返回提示用户手动输入
  const fallbackResult = {
    text: '语音识别暂时不可用，请手动输入',
    confidence: 0.0
  }

  const parsedData = {
    amount: null,
    category: 'other',
    categoryName: '其他',
    description: '语音识别失败，请手动编辑'
  }

  return {
    success: true,
    data: {
      originalText: fallbackResult.text,
      confidence: fallbackResult.confidence,
      parsedData: parsedData,
      fileID: data.fileID,
      duration: data.duration,
      isFallback: true,
      timestamp: new Date().toISOString()
    },
    message: '语音识别服务暂时不可用，已启用降级模式'
  }
}

// 保存识别记录
async function saveRecognitionLog(openid, fileID, text, parsedData) {
  try {
    await db.collection('voice_recognition_logs').add({
      data: {
        openid: openid,
        fileID: fileID,
        originalText: text,
        parsedData: parsedData,
        createTime: new Date(),
        success: true
      }
    })
  } catch (error) {
    // 只在开发环境输出详细错误
    if (process.env.NODE_ENV === 'development') {
      console.error('保存识别记录失败:', error)
    }
    // 不抛出错误，避免影响主流程
  }
}

// 保存语音记录
async function saveVoiceRecord(data, openid) {
  const {
    originalText,
    parsedData,
    fileID,
    duration,
    recordMode
  } = data

  if (!originalText || !parsedData) {
    return {
      success: false,
      error: 'MISSING_DATA',
      message: '语音数据不完整'
    }
  }

  try {
    const voiceRecord = {
      openid: openid,
      originalText: originalText,
      parsedData: parsedData,
      fileID: fileID,
      duration: duration || 0,
      recordMode: recordMode || 'travel',
      createTime: new Date(),
      updateTime: new Date()
    }

    const result = await db.collection('voice_records').add({
      data: voiceRecord
    })

    return {
      success: true,
      data: {
        _id: result._id,
        ...voiceRecord
      },
      message: '语音记录保存成功'
    }

  } catch (error) {
    console.error('保存语音记录失败:', error)
    return {
      success: false,
      error: 'SAVE_FAILED',
      message: '保存语音记录失败'
    }
  }
}

// 获取语音历史记录
async function getVoiceHistory(data, openid) {
  const { limit = 20, skip = 0 } = data

  try {
    const result = await db.collection('voice_records')
      .where({
        openid: openid
      })
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      message: '获取语音历史成功'
    }

  } catch (error) {
    console.error('获取语音历史失败:', error)
    return {
      success: false,
      error: 'GET_HISTORY_FAILED',
      message: '获取语音历史失败'
    }
  }
}

// 解析语音文字
function parseVoiceText(text) {
  const result = {
    amount: null,
    category: null,
    categoryName: '',
    description: '',
    confidence: 0,
    keywords: []
  }

  if (!text || typeof text !== 'string') {
    return result
  }

  const cleanText = text.trim().toLowerCase()

  // 提取金额 - 增强版本
  const amountPatterns = [
    /(\d+(?:\.\d{1,2})?)\s*(?:元|块|块钱|元钱|rmb)/gi,
    /(?:花了|花费|支出|消费|付了|付款)\s*(\d+(?:\.\d{1,2})?)\s*(?:元|块|块钱|元钱|rmb)?/gi,
    /(\d+(?:\.\d{1,2})?)\s*(?:块|元)/gi,
    /(?:一共|总共|合计)\s*(\d+(?:\.\d{1,2})?)\s*(?:元|块|块钱|元钱|rmb)?/gi,
    /(\d+(?:\.\d{1,2})?)\s*(?:多|左右|大概|约)/gi
  ]

  for (let pattern of amountPatterns) {
    const matches = [...cleanText.matchAll(pattern)]
    if (matches.length > 0) {
      // 取最后一个匹配的金额（通常是最准确的）
      const lastMatch = matches[matches.length - 1]
      const amount = parseFloat(lastMatch[1])
      if (amount > 0 && amount < 100000) { // 合理范围检查
        result.amount = amount
        result.confidence += 0.3
        break
      }
    }
  }

  // 分类识别
  const categories = {
    transport: { name: '交通', keywords: ['打车', '出租车', '地铁', '公交', '飞机', '火车', '高铁'] },
    accommodation: { name: '住宿', keywords: ['酒店', '宾馆', '民宿', '住宿'] },
    food: { name: '餐饮', keywords: ['吃饭', '餐厅', '小吃', '早餐', '午餐', '晚餐', '喝水', '咖啡'] },
    attraction: { name: '景点', keywords: ['门票', '景点', '博物馆', '公园'] },
    shopping: { name: '购物', keywords: ['买', '购物', '纪念品', '特产'] },
    other: { name: '其他', keywords: ['其他', '杂费'] }
  }

  for (let [key, category] of Object.entries(categories)) {
    for (let keyword of category.keywords) {
      if (text.includes(keyword)) {
        result.category = key
        result.categoryName = category.name
        break
      }
    }
    if (result.category) break
  }

  // 如果没有识别到分类，默认为其他
  if (!result.category) {
    result.category = 'other'
    result.categoryName = '其他'
  }

  // 生成描述
  let description = text
    .replace(/(\d+(?:\.\d+)?)\s*(?:元|块|块钱|元钱)/g, '')
    .replace(/花了\s*/g, '')
    .replace(/我|刚才|刚刚/g, '')
    .trim()

  result.description = description || '语音记账'

  return result
}
