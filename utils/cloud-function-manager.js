/**
 * 全局云函数调用管理器
 * 解决云函数过度调用问题，提供统一的调用控制、缓存和去重机制
 * 集成调用监控，防止过度调用
 */

import cloudFunctionMonitor from './cloud-function-monitor.js'
class CloudFunctionManager {
  constructor() {
    // 调用记录
    this.callHistory = new Map()

    // 正在进行的调用
    this.pendingCalls = new Map()

    // 缓存
    this.cache = new Map()

    // 最后调用时间记录
    this.lastCallTimes = new Map()
    
    // 配置
    this.config = {
      // travel云函数特殊限制
      travel: {
        minInterval: 3000,        // 最小调用间隔3秒
        maxConcurrent: 5,         // 最大并发数（调整为5）
        cacheTime: 20000,         // 缓存20秒
        rateLimitActions: {       // 特定action的限制
          'getTravelPlans': 5000,       // 5秒（减少限制）
          'getCurrentPlan': 3000,       // 3秒
          'getOngoingPlans': 5000,      // 5秒
          'getTravelData': 8000,        // 8秒
          'checkPlanUpdates': 15000,    // 15秒
          'batchUpdatePlanStatus': 3600000 // 1小时
        }
      },
      
      // ai-travel云函数限制
      'ai-travel': {
        minInterval: 3000,        // 最小调用间隔3秒
        maxConcurrent: 1,         // 最大并发数1
        cacheTime: 60000,         // 缓存1分钟
        rateLimitActions: {
          'getAttractions': 30000,      // 30秒
          'generateMapData': 10000,     // 10秒
          'smartPlanning': 20000,       // 20秒
          'parseAndExtract': 15000      // 15秒
        }
      },
      
      // 其他云函数默认配置
      default: {
        minInterval: 1000,        // 最小调用间隔1秒
        maxConcurrent: 5,         // 最大并发数
        cacheTime: 10000          // 缓存10秒
      }
    }
    
    // 统计信息
    this.stats = {
      totalCalls: 0,
      blockedCalls: 0,
      cacheHits: 0,
      errors: 0
    }
    
    // 原始的云函数调用方法
    this.originalCallFunction = null
  }

  /**
   * 初始化管理器，劫持wx.cloud.callFunction
   */
  init() {
    if (!wx.cloud || !wx.cloud.callFunction) {
      console.warn('云开发未初始化，跳过云函数管理器初始化')
      return
    }

    // 保存原始方法
    this.originalCallFunction = wx.cloud.callFunction
    
    // 劫持云函数调用
    const self = this
    wx.cloud.callFunction = function(options) {
      return self.managedCallFunction(options)
    }
    
    console.log('云函数调用管理器已初始化')
  }

  /**
   * 受管理的云函数调用（集成监控器）
   */
  async managedCallFunction(options) {
    const { name, data } = options
    const action = data?.action || 'default'
    const callKey = this.generateCallKey(name, action, data)
    const startTime = Date.now()

    this.stats.totalCalls++

    // 详细日志
    console.log(`[云函数管理器] 收到调用请求: ${name}:${action}`, {
      总调用数: this.stats.totalCalls,
      待处理: this.pendingCalls.size,
      缓存大小: this.cache.size
    })

    try {
      // 1. 检查缓存
      const cached = this.getCache(callKey)
      if (cached) {
        this.stats.cacheHits++
        console.log(`[云函数管理器] ✅ 缓存命中: ${name}:${action}`)

        // 记录缓存命中到监控器
        cloudFunctionMonitor.recordCall(name, action, startTime, Date.now(), true, true, false)

        return { result: cached }
      }

      // 2. 检查调用频率限制
      if (this.isRateLimited(name, action)) {
        this.stats.blockedCalls++
        const lastCallTime = this.getLastCallTime(name, action)
        const waitTime = this.getWaitTime(name, action, lastCallTime)
        console.warn(`[云函数管理器] ⛔ 调用被限制: ${name}:${action}, 需等待${waitTime}ms`)

        // 记录被限制的调用到监控器
        cloudFunctionMonitor.recordCall(name, action, startTime, Date.now(), false, false, true)

        // 返回上次的缓存结果或错误
        const lastResult = this.getLastResult(callKey)
        if (lastResult) {
          console.log(`[云函数管理器] 📦 返回缓存结果替代被限制的调用`)
          return { result: lastResult }
        }

        throw new Error(`调用过于频繁，请${Math.ceil(waitTime/1000)}秒后重试`)
      }

      // 3. 检查并发限制
      if (this.isConcurrencyLimited(name)) {
        this.stats.blockedCalls++
        console.warn(`[云函数管理器] 🚫 并发限制: ${name}, 当前并发: ${this.getCurrentConcurrency(name)}`)

        // 记录被限制的调用到监控器
        cloudFunctionMonitor.recordCall(name, action, startTime, Date.now(), false, false, true)

        // 尝试返回缓存结果
        const lastResult = this.getLastResult(callKey)
        if (lastResult) {
          console.log(`[云函数管理器] 📦 返回缓存结果替代并发限制的调用`)
          return { result: lastResult }
        }

        throw new Error('系统繁忙，请稍后重试')
      }

      // 4. 检查是否有相同的调用正在进行
      const pendingCall = this.pendingCalls.get(callKey)
      if (pendingCall) {
        console.log(`[云函数管理器] ⏳ 等待相同调用完成: ${name}:${action}`)
        return await pendingCall
      }

      // 5. 执行调用
      console.log(`[云函数管理器] 🚀 开始执行调用: ${name}:${action}`)
      const callPromise = this.executeCall(options, callKey)
      this.pendingCalls.set(callKey, callPromise)

      const result = await callPromise

      // 6. 清理和缓存
      this.pendingCalls.delete(callKey)
      this.recordCall(name, action, result)
      this.setCache(callKey, result.result)

      const endTime = Date.now()
      const success = result.result?.success || false

      // 记录调用结果到监控器
      cloudFunctionMonitor.recordCall(name, action, startTime, endTime, success, false, false)

      console.log(`[云函数管理器] ✅ 调用完成: ${name}:${action}`, {
        成功: success,
        耗时: endTime - startTime
      })

      return result

    } catch (error) {
      this.stats.errors++
      this.pendingCalls.delete(callKey)

      // 记录调用失败到监控器
      cloudFunctionMonitor.recordCall(name, action, startTime, Date.now(), false, false, false)

      console.error(`[云函数管理器] ❌ 调用失败: ${name}:${action}`, error.message)
      throw error
    }
  }

  /**
   * 执行实际的云函数调用
   */
  async executeCall(options, callKey) {
    const { name, data } = options
    const action = data?.action || 'default'
    
    console.log(`[云函数管理器] 执行调用: ${name}:${action}`)
    
    // 添加超时控制
    const timeout = this.getTimeout(name)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('云函数调用超时')), timeout)
    })
    
    const callPromise = this.originalCallFunction.call(wx.cloud, options)
    
    return await Promise.race([callPromise, timeoutPromise])
  }

  /**
   * 生成调用键
   */
  generateCallKey(name, action, data) {
    // 对于某些action，包含关键参数以区分不同调用
    let keyData = { action }
    
    if (name === 'travel') {
      if (action === 'getTravelPlan' && data?.data?.planId) {
        keyData.planId = data.data.planId
      }
    }
    
    return `${name}:${JSON.stringify(keyData)}`
  }

  /**
   * 检查调用频率限制
   */
  isRateLimited(name, action) {
    const config = this.config[name] || this.config.default
    const actionLimit = config.rateLimitActions?.[action]
    const minInterval = actionLimit || config.minInterval
    
    const lastCallTime = this.getLastCallTime(name, action)
    if (!lastCallTime) return false
    
    return Date.now() - lastCallTime < minInterval
  }

  /**
   * 检查并发限制
   */
  isConcurrencyLimited(name) {
    const config = this.config[name] || this.config.default
    const currentConcurrent = this.getCurrentConcurrency(name)

    return currentConcurrent >= config.maxConcurrent
  }

  /**
   * 获取当前并发数
   */
  getCurrentConcurrency(name) {
    return Array.from(this.pendingCalls.keys())
      .filter(key => key.startsWith(name + ':')).length
  }

  /**
   * 获取调用开始时间（用于计算耗时）
   */
  getCallStartTime(callKey) {
    // 简单实现，实际可以更精确
    return Date.now()
  }

  /**
   * 获取上次调用时间
   */
  getLastCallTime(name, action) {
    const key = `${name}:${action}`
    return this.callHistory.get(key)?.timestamp || 0
  }

  /**
   * 获取等待时间
   */
  getWaitTime(name, action, lastCallTime) {
    const config = this.config[name] || this.config.default
    const actionLimit = config.rateLimitActions?.[action]
    const minInterval = actionLimit || config.minInterval
    
    return minInterval - (Date.now() - lastCallTime)
  }

  /**
   * 记录调用
   */
  recordCall(name, action, result) {
    const key = `${name}:${action}`
    this.callHistory.set(key, {
      timestamp: Date.now(),
      success: result.result?.success || false
    })
  }

  /**
   * 缓存相关方法
   */
  getCache(key) {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }

  setCache(key, data) {
    const name = key.split(':')[0]
    const config = this.config[name] || this.config.default
    
    this.cache.set(key, {
      data: data,
      timestamp: Date.now(),
      ttl: config.cacheTime
    })
  }

  getLastResult(key) {
    const cached = this.cache.get(key)
    return cached?.data || null
  }

  /**
   * 获取超时时间
   */
  getTimeout(name) {
    const timeouts = {
      'travel': 10000,
      'ai-travel': 30000,
      'login': 8000,
      'system': 5000
    }
    return timeouts[name] || 10000
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      cacheHitRate: this.stats.cacheHits / this.stats.totalCalls || 0,
      blockRate: this.stats.blockedCalls / this.stats.totalCalls || 0,
      pendingCalls: this.pendingCalls.size,
      cacheSize: this.cache.size
    }
  }

  /**
   * 清理过期缓存（优化版，添加安全检查）
   */
  cleanup() {
    try {
      const now = Date.now()
      let cleanedCount = 0

      // 清理过期缓存
      if (this.cache && this.cache.entries) {
        for (const [key, cached] of this.cache.entries()) {
          if (cached && now - cached.timestamp > cached.ttl) {
            this.cache.delete(key)
            cleanedCount++
          }
        }
      }

      // 清理过期的调用记录
      if (this.lastCallTimes && this.lastCallTimes.entries) {
        for (const [key, time] of this.lastCallTimes.entries()) {
          if (now - time > 300000) { // 5分钟后清理
            this.lastCallTimes.delete(key)
          }
        }
      }

      // 智能缓存大小控制
      if (this.cache && this.cache.size > 50) {
        const sortedEntries = Array.from(this.cache.entries())
          .sort((a, b) => a[1].timestamp - b[1].timestamp)

        // 删除最旧的20%缓存
        const deleteCount = Math.floor(this.cache.size * 0.2)
        for (let i = 0; i < deleteCount; i++) {
          this.cache.delete(sortedEntries[i][0])
          cleanedCount++
        }
      }

      // 只在开发环境输出清理日志
      if (cleanedCount > 0 && typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
        console.log(`[云函数管理器] 🧹 清理了 ${cleanedCount} 个过期缓存`)
      }
    } catch (error) {
      console.error('[云函数管理器] 清理过程出错:', error)
    }
  }

  /**
   * 重置统计
   */
  resetStats() {
    this.stats = {
      totalCalls: 0,
      blockedCalls: 0,
      cacheHits: 0,
      errors: 0
    }
  }

  /**
   * 测试方法 - 模拟快速调用来验证限制机制
   */
  async testRateLimit() {
    console.log('[云函数管理器] 开始测试频率限制...')

    const testCalls = []
    for (let i = 0; i < 5; i++) {
      testCalls.push(
        wx.cloud.callFunction({
          name: 'travel',
          data: {
            action: 'getTravelPlans',
            data: { limit: 1 }
          }
        }).catch(error => ({ error: error.message }))
      )
    }

    const results = await Promise.allSettled(testCalls)
    console.log('[云函数管理器] 测试结果:', results)
    console.log('[云函数管理器] 统计信息:', this.getStats())

    return results
  }

  /**
   * 获取统计信息（集成监控器数据）
   */
  getStats() {
    const managerStats = {
      ...this.stats,
      缓存命中率: this.stats.totalCalls > 0 ?
        `${(this.stats.cacheHits / this.stats.totalCalls * 100).toFixed(1)}%` : '0%',
      调用阻止率: this.stats.totalCalls > 0 ?
        `${(this.stats.blockedCalls / this.stats.totalCalls * 100).toFixed(1)}%` : '0%',
      错误率: this.stats.totalCalls > 0 ?
        `${(this.stats.errors / this.stats.totalCalls * 100).toFixed(1)}%` : '0%'
    }

    // 获取监控器的详细报告
    const monitorReport = cloudFunctionMonitor.getReport()

    return {
      manager: managerStats,
      monitor: monitorReport,
      summary: {
        totalCalls: this.stats.totalCalls,
        cacheHitRate: managerStats.缓存命中率,
        blockRate: managerStats.调用阻止率,
        errorRate: managerStats.错误率,
        averageResponseTime: monitorReport.averageResponseTime,
        activeAlerts: monitorReport.activeAlerts.length
      }
    }
  }

  /**
   * 获取监控报告
   */
  getMonitorReport() {
    return cloudFunctionMonitor.getReport()
  }

  /**
   * 重置所有统计数据
   */
  resetAllStats() {
    // 重置管理器统计
    this.stats = {
      totalCalls: 0,
      cacheHits: 0,
      blockedCalls: 0,
      errors: 0
    }

    // 重置监控器统计
    cloudFunctionMonitor.resetStats()

    console.log('云函数管理器和监控器统计数据已重置')
  }

  /**
   * 获取详细状态信息
   */
  getDetailedStatus() {
    return {
      stats: this.getStats(),
      pendingCalls: Array.from(this.pendingCalls.keys()),
      cacheKeys: Array.from(this.cache.keys()),
      callHistory: Array.from(this.callHistory.entries()).map(([key, value]) => ({
        key,
        timestamp: new Date(value.timestamp).toLocaleTimeString(),
        success: value.success
      }))
    }
  }
}

// 创建全局实例
const cloudFunctionManager = new CloudFunctionManager()

export default cloudFunctionManager
