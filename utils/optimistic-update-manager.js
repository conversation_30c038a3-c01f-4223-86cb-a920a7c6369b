/**
 * 乐观更新管理器
 * 用户操作立即更新UI，后台异步同步，失败时静默重试
 */

class OptimisticUpdateManager {
  constructor() {
    // 待同步队列
    this.syncQueue = new Map()
    
    // 重试队列
    this.retryQueue = new Map()
    
    // 操作历史（用于回滚）
    this.operationHistory = new Map()
    
    // 配置
    this.config = {
      maxRetries: 3,           // 最大重试次数
      retryDelay: 2000,        // 重试延迟
      batchSize: 5,            // 批量同步大小
      syncInterval: 30000,     // 同步间隔（从5秒延长到30秒，减少与WebSocket冲突）
      criticalActions: [       // 关键操作，失败时必须通知用户
        'deleteRecord',
        'deletePlan', 
        'payment',
        'refund'
      ]
    }
    
    // 状态
    this.isSyncing = false
    this.lastSyncTime = 0
    
    // 启动后台同步
    this.startBackgroundSync()
  }

  /**
   * 乐观更新 - 立即更新UI，后台同步
   * @param {string} action 操作类型
   * @param {object} data 操作数据
   * @param {function} uiUpdateFn UI更新函数
   * @param {function} syncFn 同步函数
   * @param {object} options 选项
   */
  async optimisticUpdate(action, data, uiUpdateFn, syncFn, options = {}) {
    const operationId = this.generateOperationId()
    const { critical = false, rollbackFn = null } = options
    
    try {
      // 1. 立即更新UI
      const previousState = await uiUpdateFn(data)
      
      // 2. 记录操作历史（用于回滚）
      this.operationHistory.set(operationId, {
        action,
        data,
        previousState,
        rollbackFn,
        timestamp: Date.now(),
        critical
      })
      
      // 3. 添加到同步队列
      this.addToSyncQueue(operationId, action, data, syncFn, critical)
      
      return { success: true, operationId }
      
    } catch (error) {
      console.error('乐观更新失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 添加到同步队列
   */
  addToSyncQueue(operationId, action, data, syncFn, critical) {
    this.syncQueue.set(operationId, {
      action,
      data,
      syncFn,
      critical,
      retryCount: 0,
      timestamp: Date.now()
    })
    
    // 关键操作立即同步
    if (critical) {
      this.syncOperation(operationId)
    }
  }

  /**
   * 同步单个操作
   */
  async syncOperation(operationId) {
    const operation = this.syncQueue.get(operationId)
    if (!operation) return
    
    try {
      // 执行同步
      const result = await operation.syncFn(operation.data)
      
      if (result.success) {
        // 同步成功，移除队列
        this.syncQueue.delete(operationId)
        this.operationHistory.delete(operationId)
        console.log(`操作同步成功: ${operation.action}`)
      } else {
        throw new Error(result.message || '同步失败')
      }
      
    } catch (error) {
      console.error(`操作同步失败: ${operation.action}`, error)
      
      // 增加重试次数
      operation.retryCount++
      
      if (operation.retryCount >= this.config.maxRetries) {
        // 达到最大重试次数
        await this.handleSyncFailure(operationId, error)
      } else {
        // 添加到重试队列
        this.addToRetryQueue(operationId)
      }
    }
  }

  /**
   * 处理同步失败
   */
  async handleSyncFailure(operationId, error) {
    const operation = this.syncQueue.get(operationId)
    const history = this.operationHistory.get(operationId)
    
    if (!operation || !history) return
    
    // 移除队列
    this.syncQueue.delete(operationId)
    
    if (operation.critical) {
      // 关键操作失败，需要回滚并通知用户
      await this.rollbackOperation(operationId)
      this.notifyUser('操作失败', `${operation.action}操作失败，已自动撤销`, 'error')
    } else {
      // 非关键操作失败，静默处理
      console.warn(`非关键操作失败，已忽略: ${operation.action}`)
      this.operationHistory.delete(operationId)
    }
  }

  /**
   * 回滚操作
   */
  async rollbackOperation(operationId) {
    const history = this.operationHistory.get(operationId)
    if (!history) return
    
    try {
      if (history.rollbackFn) {
        await history.rollbackFn(history.previousState)
      }
      console.log(`操作已回滚: ${history.action}`)
    } catch (error) {
      console.error('回滚操作失败:', error)
    } finally {
      this.operationHistory.delete(operationId)
    }
  }

  /**
   * 添加到重试队列
   */
  addToRetryQueue(operationId) {
    const operation = this.syncQueue.get(operationId)
    if (!operation) return
    
    const retryDelay = this.config.retryDelay * operation.retryCount
    
    setTimeout(() => {
      if (this.syncQueue.has(operationId)) {
        this.syncOperation(operationId)
      }
    }, retryDelay)
  }

  /**
   * 启动后台同步
   */
  startBackgroundSync() {
    setInterval(() => {
      if (!this.isSyncing && this.syncQueue.size > 0) {
        this.batchSync()
      }
    }, this.config.syncInterval)
  }

  /**
   * 批量同步
   */
  async batchSync() {
    if (this.isSyncing) return
    
    this.isSyncing = true
    
    try {
      const operations = Array.from(this.syncQueue.entries())
        .filter(([_, op]) => !op.critical) // 只处理非关键操作
        .slice(0, this.config.batchSize)
      
      const syncPromises = operations.map(([operationId]) => 
        this.syncOperation(operationId)
      )
      
      await Promise.allSettled(syncPromises)
      
    } catch (error) {
      console.error('批量同步失败:', error)
    } finally {
      this.isSyncing = false
      this.lastSyncTime = Date.now()
    }
  }

  /**
   * 生成操作ID
   */
  generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 通知用户
   */
  notifyUser(title, content, type = 'info') {
    if (type === 'error') {
      wx.showModal({
        title,
        content,
        showCancel: false,
        confirmText: '知道了'
      })
    } else {
      wx.showToast({
        title: content,
        icon: type === 'success' ? 'success' : 'none'
      })
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      queueSize: this.syncQueue.size,
      historySize: this.operationHistory.size,
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime
    }
  }

  /**
   * 清理过期操作
   */
  cleanup() {
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    
    // 清理过期的操作历史
    for (const [operationId, history] of this.operationHistory.entries()) {
      if (now - history.timestamp > maxAge) {
        this.operationHistory.delete(operationId)
      }
    }
  }
}

// 创建全局实例
const optimisticUpdateManager = new OptimisticUpdateManager()

// 定期清理
setInterval(() => {
  optimisticUpdateManager.cleanup()
}, 60 * 60 * 1000) // 每小时清理一次

export default optimisticUpdateManager
export { OptimisticUpdateManager }
