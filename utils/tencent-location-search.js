// utils/tencent-location-search.js
// 腾讯位置服务搜索封装 - 基于官方JavaScript SDK

/**
 * 腾讯位置服务搜索类
 * 提供地点搜索和输入提示功能
 */
class TencentLocationSearch {
  constructor() {
    // 腾讯位置服务API密钥
    this.key = 'J6MBZ-CSYWW-CXHRT-34E27-DCAUS-7YFZX'
    this.referer = '爱巢小记'
    this.baseURL = 'https://apis.map.qq.com'
    
    // 缓存机制
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
    
    // 防抖定时器
    this.debounceTimer = null
    this.debounceDelay = 300 // 300ms防抖
  }

  /**
   * 发送API请求
   * @param {string} endpoint API端点
   * @param {object} params 请求参数
   * @returns {Promise} API响应
   */
  async request(endpoint, params = {}) {
    try {
      // 构建请求参数
      const queryParams = {
        key: this.key,
        ...params
      }
      
      // 构建URL
      const url = `${this.baseURL}${endpoint}?${new URLSearchParams(queryParams)}`
      
      console.log('腾讯位置服务API请求:', { endpoint, params })
      
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: url,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        })
      })

      if (response.statusCode !== 200) {
        throw new Error(`HTTP ${response.statusCode}: 请求失败`)
      }

      const data = response.data
      
      // 检查API响应状态
      if (data.status !== 0) {
        throw new Error(`腾讯位置服务API错误: ${data.status} - ${data.message}`)
      }

      return data
    } catch (error) {
      console.error('腾讯位置服务API请求失败:', error)
      throw error
    }
  }

  /**
   * 获取输入提示
   * @param {string} keyword 搜索关键词
   * @param {object} options 搜索选项
   * @returns {Promise} 搜索建议列表
   */
  async getSuggestion(keyword, options = {}) {
    try {
      if (!keyword || keyword.trim().length === 0) {
        return {
          success: true,
          data: [],
          count: 0
        }
      }

      const trimmedKeyword = keyword.trim()
      
      // 检查缓存
      const cacheKey = `suggestion_${trimmedKeyword}_${JSON.stringify(options)}`
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }

      const {
        region = '', // 限制城市范围
        region_fix = 0, // 是否固定在当前城市
        policy = 0, // 检索策略
        location = '', // 定位坐标
        get_subpois = 0, // 是否返回子地点
        page_size = 10, // 每页条目数
        page_index = 1 // 页码
      } = options

      const params = {
        keyword: trimmedKeyword,
        region,
        region_fix,
        policy,
        location,
        get_subpois,
        page_size: Math.min(page_size, 20),
        page_index,
        output: 'json'
      }

      const result = await this.request('/ws/place/v1/suggestion', params)
      
      // 处理搜索结果
      const suggestions = (result.data || []).map(item => ({
        id: item.id,
        title: item.title,
        address: item.address,
        province: item.province,
        city: item.city,
        district: item.district,
        location: {
          latitude: item.location.lat,
          longitude: item.location.lng
        },
        type: item.type,
        category: item.category || 'destination'
      }))

      const response = {
        success: true,
        data: suggestions,
        count: result.count || suggestions.length,
        keyword: trimmedKeyword
      }

      // 缓存结果
      this.setCache(cacheKey, response)
      
      return response
    } catch (error) {
      console.error('获取搜索建议失败:', error)
      return {
        success: false,
        message: error.message || '获取搜索建议失败',
        data: [],
        count: 0
      }
    }
  }

  /**
   * 地点搜索
   * @param {string} keyword 搜索关键词
   * @param {object} options 搜索选项
   * @returns {Promise} 搜索结果
   */
  async search(keyword, options = {}) {
    try {
      if (!keyword || keyword.trim().length === 0) {
        return {
          success: true,
          data: [],
          count: 0
        }
      }

      const trimmedKeyword = keyword.trim()
      
      // 检查缓存
      const cacheKey = `search_${trimmedKeyword}_${JSON.stringify(options)}`
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }

      const {
        location = '', // 位置坐标
        region = '', // 指定地区名称
        rectangle = '', // 矩形区域范围
        auto_extend = 1, // 自动扩大范围
        filter = '', // 分类过滤
        page_size = 10, // 每页条目数
        page_index = 1, // 页码
        address_format = 'long' // 地址格式
      } = options

      const params = {
        keyword: trimmedKeyword,
        location,
        region,
        rectangle,
        auto_extend,
        filter,
        page_size: Math.min(page_size, 20),
        page_index,
        address_format,
        output: 'json'
      }

      const result = await this.request('/ws/place/v1/search', params)
      
      // 处理搜索结果
      const places = (result.data || []).map(item => ({
        id: item.id,
        title: item.title,
        address: item.address,
        tel: item.tel,
        category: item.category,
        type: item.type,
        location: {
          latitude: item.location.lat,
          longitude: item.location.lng
        },
        ad_info: item.ad_info,
        boundary: item.boundary,
        pano: item.pano
      }))

      const response = {
        success: true,
        data: places,
        count: result.count || places.length,
        keyword: trimmedKeyword
      }

      // 缓存结果
      this.setCache(cacheKey, response)
      
      return response
    } catch (error) {
      console.error('地点搜索失败:', error)
      return {
        success: false,
        message: error.message || '地点搜索失败',
        data: [],
        count: 0
      }
    }
  }

  /**
   * 防抖搜索
   * @param {string} keyword 搜索关键词
   * @param {function} callback 回调函数
   * @param {object} options 搜索选项
   */
  debouncedSuggestion(keyword, callback, options = {}) {
    // 清除之前的定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    // 设置新的定时器
    this.debounceTimer = setTimeout(async () => {
      try {
        const result = await this.getSuggestion(keyword, options)
        callback(result)
      } catch (error) {
        callback({
          success: false,
          message: error.message,
          data: [],
          count: 0
        })
      }
    }, this.debounceDelay)
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {object|null} 缓存数据
   */
  getFromCache(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }
    return null
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {object} data 缓存数据
   */
  setCache(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    })
    
    // 清理过期缓存
    this.cleanExpiredCache()
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now()
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheTimeout) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear()
  }

  /**
   * 逆地址解析 - 根据坐标获取地址信息
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   * @param {object} options 解析选项
   * @returns {Promise} 解析结果
   */
  async reverseGeocode(latitude, longitude, options = {}) {
    try {
      if (!latitude || !longitude) {
        return {
          success: false,
          message: '坐标参数不能为空'
        }
      }

      // 检查缓存
      const cacheKey = `reverse_${latitude}_${longitude}`
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return cached
      }

      const {
        get_poi = 1,        // 是否返回周边POI列表
        poi_options = '',   // POI召回策略
        coord_type = 5,     // 输入坐标类型
        output = 'json'     // 输出格式
      } = options

      const params = {
        location: `${latitude},${longitude}`,
        get_poi,
        poi_options,
        coord_type,
        output
      }

      const result = await this.request('/ws/geocoder/v1/', params)

      if (result.status === 0 && result.result) {
        const addressInfo = result.result

        const response = {
          success: true,
          data: {
            name: addressInfo.formatted_addresses?.recommend ||
                  addressInfo.address ||
                  '未知位置',
            address: addressInfo.address || '',
            formatted_address: addressInfo.formatted_addresses?.recommend || addressInfo.address,
            location: {
              latitude: latitude,
              longitude: longitude
            },
            ad_info: addressInfo.ad_info || {},
            address_components: addressInfo.address_components || {},
            pois: addressInfo.pois || []
          }
        }

        // 缓存结果
        this.setCache(cacheKey, response)
        return response
      } else {
        return {
          success: false,
          message: result.message || '逆地址解析失败'
        }
      }
    } catch (error) {
      console.error('逆地址解析失败:', error)
      return {
        success: false,
        message: error.message || '逆地址解析请求失败'
      }
    }
  }
}

// 创建单例实例
const tencentLocationSearch = new TencentLocationSearch()

module.exports = tencentLocationSearch
