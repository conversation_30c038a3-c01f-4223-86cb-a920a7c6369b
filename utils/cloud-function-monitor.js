/**
 * 云函数调用监控器
 * 实时监控云函数调用情况，防止过度调用
 */

class CloudFunctionMonitor {
  constructor() {
    // 调用统计
    this.stats = {
      totalCalls: 0,
      successCalls: 0,
      failedCalls: 0,
      cachedCalls: 0,
      deferredCalls: 0,
      callsByFunction: new Map(),
      callsByHour: new Map(),
      averageResponseTime: 0,
      lastResetTime: Date.now()
    }

    // 调用历史（最近100次调用）
    this.callHistory = []
    this.maxHistorySize = 100

    // 告警配置
    this.alertConfig = {
      maxCallsPerMinute: 30,
      maxCallsPerHour: 500,
      maxFailureRate: 0.2, // 20%失败率
      maxResponseTime: 10000 // 10秒
    }

    // 告警状态
    this.alerts = []
    this.isMonitoring = false

    // 开始监控
    this.startMonitoring()
  }

  /**
   * 记录云函数调用
   */
  recordCall(functionName, action, startTime, endTime, success, cached = false, deferred = false) {
    const now = Date.now()
    const responseTime = endTime - startTime
    const hourKey = this.getHourKey(now)

    // 更新总体统计
    this.stats.totalCalls++
    if (success) {
      this.stats.successCalls++
    } else {
      this.stats.failedCalls++
    }
    if (cached) {
      this.stats.cachedCalls++
    }
    if (deferred) {
      this.stats.deferredCalls++
    }

    // 更新平均响应时间
    this.updateAverageResponseTime(responseTime)

    // 按函数统计
    const funcKey = `${functionName}:${action}`
    if (!this.stats.callsByFunction.has(funcKey)) {
      this.stats.callsByFunction.set(funcKey, {
        count: 0,
        successCount: 0,
        failedCount: 0,
        totalResponseTime: 0,
        avgResponseTime: 0
      })
    }
    const funcStats = this.stats.callsByFunction.get(funcKey)
    funcStats.count++
    funcStats.totalResponseTime += responseTime
    funcStats.avgResponseTime = funcStats.totalResponseTime / funcStats.count
    if (success) {
      funcStats.successCount++
    } else {
      funcStats.failedCount++
    }

    // 按小时统计
    if (!this.stats.callsByHour.has(hourKey)) {
      this.stats.callsByHour.set(hourKey, 0)
    }
    this.stats.callsByHour.set(hourKey, this.stats.callsByHour.get(hourKey) + 1)

    // 添加到调用历史
    this.addToHistory({
      functionName,
      action,
      startTime,
      endTime,
      responseTime,
      success,
      cached,
      deferred,
      timestamp: now
    })

    // 检查告警条件
    this.checkAlerts()
  }

  /**
   * 更新平均响应时间
   */
  updateAverageResponseTime(responseTime) {
    const totalResponseTime = this.stats.averageResponseTime * (this.stats.totalCalls - 1) + responseTime
    this.stats.averageResponseTime = totalResponseTime / this.stats.totalCalls
  }

  /**
   * 添加到调用历史
   */
  addToHistory(callRecord) {
    this.callHistory.push(callRecord)
    
    // 保持历史记录大小限制
    if (this.callHistory.length > this.maxHistorySize) {
      this.callHistory.shift()
    }
  }

  /**
   * 检查告警条件
   */
  checkAlerts() {
    const now = Date.now()
    
    // 检查每分钟调用次数
    const lastMinuteCalls = this.getCallsInTimeRange(now - 60000, now)
    if (lastMinuteCalls > this.alertConfig.maxCallsPerMinute) {
      this.triggerAlert('HIGH_FREQUENCY', `每分钟调用次数过高: ${lastMinuteCalls}次`)
    }

    // 检查每小时调用次数
    const lastHourCalls = this.getCallsInTimeRange(now - 3600000, now)
    if (lastHourCalls > this.alertConfig.maxCallsPerHour) {
      this.triggerAlert('HOURLY_LIMIT', `每小时调用次数过高: ${lastHourCalls}次`)
    }

    // 检查失败率
    const failureRate = this.stats.totalCalls > 0 ? this.stats.failedCalls / this.stats.totalCalls : 0
    if (failureRate > this.alertConfig.maxFailureRate) {
      this.triggerAlert('HIGH_FAILURE_RATE', `失败率过高: ${(failureRate * 100).toFixed(2)}%`)
    }

    // 检查响应时间
    if (this.stats.averageResponseTime > this.alertConfig.maxResponseTime) {
      this.triggerAlert('SLOW_RESPONSE', `平均响应时间过长: ${this.stats.averageResponseTime.toFixed(0)}ms`)
    }
  }

  /**
   * 触发告警
   */
  triggerAlert(type, message) {
    const alert = {
      type,
      message,
      timestamp: Date.now(),
      id: this.generateId()
    }

    // 避免重复告警（5分钟内相同类型的告警只触发一次）
    const recentAlert = this.alerts.find(a => 
      a.type === type && (Date.now() - a.timestamp) < 300000
    )
    
    if (!recentAlert) {
      this.alerts.push(alert)
      console.warn(`[云函数监控告警] ${type}: ${message}`)
      
      // 限制告警数量
      if (this.alerts.length > 50) {
        this.alerts = this.alerts.slice(-25)
      }
    }
  }

  /**
   * 获取时间范围内的调用次数
   */
  getCallsInTimeRange(startTime, endTime) {
    return this.callHistory.filter(call => 
      call.timestamp >= startTime && call.timestamp <= endTime
    ).length
  }

  /**
   * 获取小时键
   */
  getHourKey(timestamp) {
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}-${String(date.getHours()).padStart(2, '0')}`
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return

    this.isMonitoring = true
    
    // 每5分钟清理过期数据
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)

    // 只在开发环境输出启动日志
    if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
      console.log('云函数调用监控器已启动')
    }
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }

    // 只在开发环境输出停止日志
    if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
      console.log('云函数调用监控器已停止')
    }
  }

  /**
   * 清理过期数据
   */
  cleanup() {
    const now = Date.now()
    const oneDayAgo = now - 24 * 60 * 60 * 1000

    // 清理过期的小时统计
    for (const [hourKey, count] of this.stats.callsByHour.entries()) {
      const [year, month, day, hour] = hourKey.split('-').map(Number)
      const hourTimestamp = new Date(year, month - 1, day, hour).getTime()
      
      if (hourTimestamp < oneDayAgo) {
        this.stats.callsByHour.delete(hourKey)
      }
    }

    // 清理过期的告警
    this.alerts = this.alerts.filter(alert => 
      (now - alert.timestamp) < 24 * 60 * 60 * 1000
    )

    // 只在开发环境输出清理日志
    if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
      console.log('云函数监控数据清理完成')
    }
  }

  /**
   * 获取监控报告
   */
  getReport() {
    const now = Date.now()
    const timeSinceReset = now - this.stats.lastResetTime
    const hoursRunning = timeSinceReset / (1000 * 60 * 60)

    return {
      // 基础统计
      totalCalls: this.stats.totalCalls,
      successCalls: this.stats.successCalls,
      failedCalls: this.stats.failedCalls,
      cachedCalls: this.stats.cachedCalls,
      deferredCalls: this.stats.deferredCalls,
      
      // 比率统计
      successRate: this.stats.totalCalls > 0 ? (this.stats.successCalls / this.stats.totalCalls * 100).toFixed(2) + '%' : '0%',
      cacheHitRate: this.stats.totalCalls > 0 ? (this.stats.cachedCalls / this.stats.totalCalls * 100).toFixed(2) + '%' : '0%',
      deferredRate: this.stats.totalCalls > 0 ? (this.stats.deferredCalls / this.stats.totalCalls * 100).toFixed(2) + '%' : '0%',
      
      // 性能统计
      averageResponseTime: Math.round(this.stats.averageResponseTime) + 'ms',
      callsPerHour: hoursRunning > 0 ? Math.round(this.stats.totalCalls / hoursRunning) : 0,
      
      // 函数统计
      functionStats: Array.from(this.stats.callsByFunction.entries()).map(([funcKey, stats]) => ({
        function: funcKey,
        calls: stats.count,
        successRate: stats.count > 0 ? (stats.successCount / stats.count * 100).toFixed(1) + '%' : '0%',
        avgResponseTime: Math.round(stats.avgResponseTime) + 'ms'
      })).sort((a, b) => b.calls - a.calls),
      
      // 告警信息
      activeAlerts: this.alerts.filter(alert => (now - alert.timestamp) < 300000), // 5分钟内的告警
      totalAlerts: this.alerts.length,
      
      // 运行时间
      runningTime: this.formatDuration(timeSinceReset)
    }
  }

  /**
   * 重置统计数据
   */
  resetStats() {
    this.stats = {
      totalCalls: 0,
      successCalls: 0,
      failedCalls: 0,
      cachedCalls: 0,
      deferredCalls: 0,
      callsByFunction: new Map(),
      callsByHour: new Map(),
      averageResponseTime: 0,
      lastResetTime: Date.now()
    }
    
    this.callHistory = []
    this.alerts = []
    
    console.log('云函数监控统计数据已重置')
  }

  /**
   * 格式化持续时间
   */
  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `${days}天${hours % 24}小时`
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`
    } else {
      return `${seconds}秒`
    }
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Math.random().toString(36).substr(2, 9)
  }
}

// 创建全局实例
const cloudFunctionMonitor = new CloudFunctionMonitor()

export default cloudFunctionMonitor
