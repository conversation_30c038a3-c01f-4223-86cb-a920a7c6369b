/**
 * 简化数据管理器
 * 本地优先 + 定期备份，减少云函数调用80%
 * 集成增强缓存管理器和本地计算引擎
 */

import auth from './auth.js'
import layeredSyncManager from './layered-sync-manager.js'
import enhancedCacheManager from './enhanced-cache-manager.js'
import localComputationEngine from './local-computation-engine.js'
import smartCallScheduler from './smart-call-scheduler.js'

// 简化的缓存配置
const CACHE_CONFIG = {
  // 只保留两级缓存：内存 + 本地存储
  memory: new Map(),
  maxMemorySize: 50,
  
  // 缓存TTL（简化）
  ttl: {
    short: 2 * 60 * 1000,    // 2分钟
    medium: 10 * 60 * 1000,  // 10分钟
    long: 30 * 60 * 1000     // 30分钟
  }
}

class SimpleDataManager {
  constructor() {
    this.initialized = false
    this.db = null
    this._ = null

    // 本地计算缓存（保留兼容性）
    this.localCalculations = new Map()

    // 数据变更监听器
    this.changeListeners = new Map()

    // 集成新组件
    this.enhancedCache = enhancedCacheManager
    this.localComputation = localComputationEngine
    this.smartScheduler = smartCallScheduler

    // 初始化智能调度器
    this.smartScheduler.init()
  }

  /**
   * 初始化
   */
  async initialize() {
    if (this.initialized) return true

    try {
      if (wx.cloud) {
        this.db = wx.cloud.database()
        this._ = this.db.command
      }

      this.initialized = true
      return true
    } catch (error) {
      console.error('数据管理器初始化失败:', error)
      return false
    }
  }

  /**
   * 本地优先获取数据（增强版）
   */
  async getData(key, cloudFn = null, options = {}) {
    const {
      useCache = true,
      ttl = CACHE_CONFIG.ttl.medium,
      forceRefresh = false,
      priority = 'NORMAL',
      enableLocalComputation = false,
      computationFn = null
    } = options

    try {
      // 使用增强缓存管理器（仅当有云函数时）
      if (useCache && !forceRefresh && cloudFn && typeof cloudFn === 'function') {
        const result = await this.enhancedCache.smartGet(
          key,
          cloudFn,
          { ttl, priority, forceRefresh }
        )

        if (result.success) {
          // 如果启用本地计算，对数据进行处理
          if (enableLocalComputation && computationFn) {
            result.data = computationFn(result.data)
          }

          return result
        }
      }

      // 如果没有云函数，直接从缓存获取
      if (!cloudFn) {
        // 检查内存缓存
        const cached = this.getFromMemoryCache(key)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }

        // 检查本地存储
        const stored = this.getFromLocalStorage(key)
        if (stored) {
          this.setMemoryCache(key, stored, ttl)
          return { success: true, data: stored, fromLocal: true }
        }

        // 没有数据且没有云函数
        return { success: false, message: '没有可用的数据源' }
      }

      // 兼容原有逻辑
      return await this.getDataLegacy(key, cloudFn, options)

    } catch (error) {
      console.error('获取数据失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 原有数据获取逻辑（保持兼容性）
   */
  async getDataLegacy(key, cloudFn = null, options = {}) {
    const {
      useCache = true,
      ttl = CACHE_CONFIG.ttl.medium,
      forceRefresh = false
    } = options

    try {
      // 1. 检查内存缓存
      if (useCache && !forceRefresh) {
        const cached = this.getFromMemoryCache(key)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 2. 检查本地存储
      if (useCache && !forceRefresh) {
        const stored = this.getFromLocalStorage(key)
        if (stored) {
          // 更新内存缓存
          this.setMemoryCache(key, stored, ttl)
          return { success: true, data: stored, fromLocal: true }
        }
      }

      // 3. 云端获取（如果提供了云函数）
      if (cloudFn) {
        const result = await cloudFn()
        if (result.success) {
          // 缓存数据
          this.setMemoryCache(key, result.data, ttl)
          this.setLocalStorage(key, result.data)
          return result
        }
      }

      return { success: false, message: '数据获取失败' }

    } catch (error) {
      console.error('获取数据失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 乐观更新数据
   */
  async updateData(key, data, cloudFn, options = {}) {
    const { critical = false } = options

    // UI更新函数
    const uiUpdateFn = async (updateData) => {
      const previousData = this.getFromMemoryCache(key) || 
                          this.getFromLocalStorage(key)
      
      // 立即更新本地缓存
      this.setMemoryCache(key, updateData)
      this.setLocalStorage(key, updateData)
      
      // 通知监听器
      this.notifyChange(key, updateData)
      
      return previousData
    }

    // 同步函数
    const syncFn = async (syncData) => {
      return await cloudFn(syncData)
    }

    // 使用分层同步
    return await layeredSyncManager.layeredSync(
      `update_${key}`,
      data,
      syncFn,
      uiUpdateFn,
      {
        critical,
        rollbackFn: async (previousData) => {
          if (previousData) {
            this.setMemoryCache(key, previousData)
            this.setLocalStorage(key, previousData)
            this.notifyChange(key, previousData)
          }
        }
      }
    )
  }

  /**
   * 删除数据（关键操作）
   */
  async deleteData(key, cloudFn) {
    // UI更新函数
    const uiUpdateFn = async () => {
      const previousData = this.getFromMemoryCache(key) || 
                          this.getFromLocalStorage(key)
      
      // 立即从缓存中移除
      CACHE_CONFIG.memory.delete(key)
      wx.removeStorageSync(key)
      
      // 通知监听器
      this.notifyChange(key, null)
      
      return previousData
    }

    // 同步函数
    const syncFn = async () => {
      return await cloudFn()
    }

    // 删除操作为关键操作
    return await layeredSyncManager.layeredSync(
      `delete_${key}`,
      null,
      syncFn,
      uiUpdateFn,
      {
        critical: true,
        rollbackFn: async (previousData) => {
          if (previousData) {
            this.setMemoryCache(key, previousData)
            this.setLocalStorage(key, previousData)
            this.notifyChange(key, previousData)
          }
        }
      }
    )
  }

  /**
   * 内存缓存操作
   */
  getFromMemoryCache(key) {
    const item = CACHE_CONFIG.memory.get(key)
    if (!item) return null

    // 检查TTL
    if (Date.now() - item.timestamp > item.ttl) {
      CACHE_CONFIG.memory.delete(key)
      return null
    }

    return item.data
  }

  setMemoryCache(key, data, ttl = CACHE_CONFIG.ttl.medium) {
    // 检查内存大小限制
    if (CACHE_CONFIG.memory.size >= CACHE_CONFIG.maxMemorySize) {
      // 删除最旧的缓存
      const oldestKey = CACHE_CONFIG.memory.keys().next().value
      CACHE_CONFIG.memory.delete(oldestKey)
    }

    CACHE_CONFIG.memory.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 本地存储操作
   */
  getFromLocalStorage(key) {
    try {
      const item = wx.getStorageSync(key)
      if (!item) return null

      // 检查TTL
      if (item.expiry && Date.now() > item.expiry) {
        wx.removeStorageSync(key)
        return null
      }

      return item.data
    } catch (error) {
      return null
    }
  }

  setLocalStorage(key, data, ttl = CACHE_CONFIG.ttl.long) {
    try {
      wx.setStorageSync(key, {
        data,
        expiry: Date.now() + ttl
      })
    } catch (error) {
      console.warn('本地存储失败:', error)
    }
  }

  /**
   * 本地计算（避免云函数调用）- 增强版
   */
  calculateLocally(key, calculationFn, dependencies = []) {
    // 优先使用本地计算引擎
    if (key.includes('financial')) {
      return this.localComputation.calculateFinancialStats(dependencies[0])
    } else if (key.includes('travel')) {
      return this.localComputation.calculateTravelStats(dependencies[0])
    } else if (key.includes('activity')) {
      return this.localComputation.calculateUserActivityStats(dependencies[0], dependencies[1])
    }

    // 兼容原有逻辑
    return this.calculateLocallyLegacy(key, calculationFn, dependencies)
  }

  /**
   * 原有本地计算逻辑（保持兼容性）
   */
  calculateLocallyLegacy(key, calculationFn, dependencies = []) {
    // 检查依赖是否变更
    const depKey = dependencies.join('_')
    const cached = this.localCalculations.get(key)

    if (cached && cached.depKey === depKey) {
      return cached.result
    }

    // 执行计算
    const result = calculationFn()

    // 缓存结果
    this.localCalculations.set(key, {
      result,
      depKey,
      timestamp: Date.now()
    })

    return result
  }

  /**
   * 智能调度云函数调用
   */
  async scheduleCloudCall(cloudFunction, options = {}) {
    const {
      priority = 'NORMAL',
      canDefer = true,
      timeout = 10000,
      context = {}
    } = options

    return await this.smartScheduler.schedule(cloudFunction, {
      priority,
      canDefer,
      timeout,
      context
    })
  }

  /**
   * 监听数据变更
   */
  onChange(key, callback) {
    if (!this.changeListeners.has(key)) {
      this.changeListeners.set(key, [])
    }
    this.changeListeners.get(key).push(callback)
  }

  /**
   * 通知数据变更
   */
  notifyChange(key, data) {
    const listeners = this.changeListeners.get(key)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('数据变更监听器执行失败:', error)
        }
      })
    }
  }

  /**
   * 清理缓存
   */
  clearCache(pattern = null) {
    if (pattern) {
      // 清理匹配的缓存
      for (const key of CACHE_CONFIG.memory.keys()) {
        if (key.includes(pattern)) {
          CACHE_CONFIG.memory.delete(key)
        }
      }
    } else {
      // 清理所有缓存
      CACHE_CONFIG.memory.clear()
    }
  }

  /**
   * 获取缓存状态（增强版）
   */
  getCacheStatus() {
    return {
      // 原有状态
      memorySize: CACHE_CONFIG.memory.size,
      maxMemorySize: CACHE_CONFIG.maxMemorySize,
      localCalculations: this.localCalculations.size,
      syncStatus: layeredSyncManager.getSyncStatus(),

      // 新增状态
      enhancedCache: this.enhancedCache.getStats(),
      localComputation: this.localComputation.getCacheStatus(),
      smartScheduler: this.smartScheduler.getQueueStatus()
    }
  }

  /**
   * 批量数据操作
   */
  async batchOperation(operations, options = {}) {
    const batchManager = this.enhancedCache.createBatchManager()

    const promises = operations.map(op => {
      return batchManager.addOperation(op.type, op.data)
    })

    return await Promise.allSettled(promises)
  }

  /**
   * 更新用户活动状态
   */
  updateUserActivity(page) {
    this.smartScheduler.updateUserActivity(page)
  }

  /**
   * 清理所有缓存
   */
  clearAllCaches() {
    // 清理原有缓存
    this.clearCache()

    // 清理新组件缓存
    this.enhancedCache.clearAll()
    this.localComputation.clearCache()
  }
}

// 创建全局实例
const simpleDataManager = new SimpleDataManager()

export default simpleDataManager
export { SimpleDataManager }
