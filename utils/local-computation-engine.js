/**
 * 本地计算引擎
 * 替代简单的云函数调用，减少网络请求
 */

class LocalComputationEngine {
  constructor() {
    this.computationCache = new Map()
    this.maxCacheSize = 50
  }

  /**
   * 计算财务统计数据
   */
  calculateFinancialStats(records, options = {}) {
    const cacheKey = this.generateCacheKey('financial_stats', records, options)

    // 检查缓存
    if (this.computationCache.has(cacheKey)) {
      return this.computationCache.get(cacheKey)
    }

    // 数据类型检查和处理
    let recordsArray = []
    if (Array.isArray(records)) {
      recordsArray = records
    } else if (records && typeof records === 'object') {
      // 如果是对象，尝试提取数组字段
      if (Array.isArray(records.records)) {
        recordsArray = records.records
      } else if (Array.isArray(records.data)) {
        recordsArray = records.data
      } else if (Array.isArray(records.list)) {
        recordsArray = records.list
      } else {
        console.warn('传入的records不是数组，也没有找到数组字段:', records)
        recordsArray = []
      }
    } else if (records === null || records === undefined) {
      recordsArray = []
    } else {
      console.warn('传入的records类型不正确:', typeof records, records)
      recordsArray = []
    }

    const stats = {
      totalExpense: 0,
      dailyExpense: 0,
      travelExpense: 0,
      monthlyExpense: 0,
      weeklyExpense: 0,
      categoryStats: {},
      trendData: [],
      avgDailyExpense: 0,
      maxSingleExpense: 0,
      expenseCount: recordsArray.length
    }

    if (!recordsArray || recordsArray.length === 0) {
      this.cacheResult(cacheKey, stats)
      return stats
    }

    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    // 处理每条记录
    recordsArray.forEach(record => {
      const amount = Number(record.amount) || 0
      const recordDate = new Date(record.date)
      
      stats.totalExpense += amount
      
      // 更新最大单笔支出
      if (amount > stats.maxSingleExpense) {
        stats.maxSingleExpense = amount
      }

      // 按类型分类
      if (record.type === 'daily') {
        stats.dailyExpense += amount
      } else if (record.type === 'travel') {
        stats.travelExpense += amount
      }

      // 本月支出
      if (recordDate.getMonth() === currentMonth && 
          recordDate.getFullYear() === currentYear) {
        stats.monthlyExpense += amount
      }

      // 本周支出
      if (recordDate >= weekAgo) {
        stats.weeklyExpense += amount
      }

      // 分类统计
      const category = record.category || '其他'
      if (!stats.categoryStats[category]) {
        stats.categoryStats[category] = {
          amount: 0,
          count: 0,
          percentage: 0
        }
      }
      stats.categoryStats[category].amount += amount
      stats.categoryStats[category].count += 1
    })

    // 计算分类百分比
    Object.keys(stats.categoryStats).forEach(category => {
      const categoryData = stats.categoryStats[category]
      categoryData.percentage = stats.totalExpense > 0 ? 
        (categoryData.amount / stats.totalExpense * 100).toFixed(1) : 0
    })

    // 计算平均日支出
    const daysSinceFirstRecord = this.calculateDaysSinceFirstRecord(recordsArray)
    stats.avgDailyExpense = daysSinceFirstRecord > 0 ?
      (stats.totalExpense / daysSinceFirstRecord).toFixed(2) : 0

    // 生成趋势数据
    stats.trendData = this.generateTrendData(recordsArray)

    // 缓存结果
    this.cacheResult(cacheKey, stats)
    
    return stats
  }

  /**
   * 计算旅行统计数据
   */
  calculateTravelStats(plans, options = {}) {
    const cacheKey = this.generateCacheKey('travel_stats', plans, options)

    if (this.computationCache.has(cacheKey)) {
      return this.computationCache.get(cacheKey)
    }

    // 数据类型检查和处理
    let plansArray = []
    if (Array.isArray(plans)) {
      plansArray = plans
    } else if (plans && typeof plans === 'object') {
      // 如果是对象，尝试提取数组字段
      if (Array.isArray(plans.plans)) {
        plansArray = plans.plans
      } else if (Array.isArray(plans.data)) {
        plansArray = plans.data
      } else if (Array.isArray(plans.list)) {
        plansArray = plans.list
      } else {
        console.warn('传入的plans不是数组，也没有找到数组字段:', plans)
        plansArray = []
      }
    } else if (plans === null || plans === undefined) {
      plansArray = []
    } else {
      console.warn('传入的plans类型不正确:', typeof plans, plans)
      plansArray = []
    }

    const stats = {
      totalPlans: plansArray.length,
      completedPlans: 0,
      ongoingPlans: 0,
      plannedPlans: 0,
      cancelledPlans: 0,
      totalExpense: 0,
      avgExpensePerTrip: 0,
      totalDays: 0,
      avgDaysPerTrip: 0,
      popularDestinations: [],
      monthlyPlanCount: {},
      collaborationStats: {
        totalCollaborators: 0,
        avgCollaboratorsPerPlan: 0
      }
    }

    if (!plansArray || plansArray.length === 0) {
      this.cacheResult(cacheKey, stats)
      return stats
    }

    const now = new Date()
    const destinationCount = {}
    let totalCollaborators = 0

    plansArray.forEach(plan => {
      const startDate = new Date(plan.startDate)
      const endDate = new Date(plan.endDate)
      
      // 计划状态分类
      if (plan.status === 'cancelled') {
        stats.cancelledPlans++
      } else if (endDate < now) {
        stats.completedPlans++
      } else if (startDate <= now && endDate >= now) {
        stats.ongoingPlans++
      } else {
        stats.plannedPlans++
      }

      // 支出统计
      if (plan.totalExpense) {
        stats.totalExpense += Number(plan.totalExpense)
      }

      // 天数统计
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
      stats.totalDays += days

      // 目的地统计
      if (plan.destination) {
        destinationCount[plan.destination] = (destinationCount[plan.destination] || 0) + 1
      }

      // 月份统计
      const monthKey = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`
      stats.monthlyPlanCount[monthKey] = (stats.monthlyPlanCount[monthKey] || 0) + 1

      // 协作统计
      if (plan.collaboration && plan.collaboration.collaborators) {
        totalCollaborators += plan.collaboration.collaborators.length
      }
    })

    // 计算平均值
    const completedPlans = stats.completedPlans || 1
    stats.avgExpensePerTrip = (stats.totalExpense / completedPlans).toFixed(2)
    stats.avgDaysPerTrip = (stats.totalDays / stats.totalPlans).toFixed(1)

    // 协作统计
    stats.collaborationStats.totalCollaborators = totalCollaborators
    stats.collaborationStats.avgCollaboratorsPerPlan = 
      (totalCollaborators / stats.totalPlans).toFixed(1)

    // 热门目的地
    stats.popularDestinations = Object.entries(destinationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([destination, count]) => ({ destination, count }))

    this.cacheResult(cacheKey, stats)
    return stats
  }

  /**
   * 计算用户活跃度统计
   */
  calculateUserActivityStats(records, plans, options = {}) {
    const cacheKey = this.generateCacheKey('user_activity', { records, plans }, options)
    
    if (this.computationCache.has(cacheKey)) {
      return this.computationCache.get(cacheKey)
    }

    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const stats = {
      totalRecords: records.length,
      totalPlans: plans.length,
      recentActivity: {
        last7Days: {
          records: 0,
          plans: 0,
          expenses: 0
        },
        last30Days: {
          records: 0,
          plans: 0,
          expenses: 0
        }
      },
      activityScore: 0,
      streakDays: 0
    }

    // 统计最近活动
    records.forEach(record => {
      const recordDate = new Date(record.date)
      const amount = Number(record.amount) || 0
      
      if (recordDate >= sevenDaysAgo) {
        stats.recentActivity.last7Days.records++
        stats.recentActivity.last7Days.expenses += amount
      }
      
      if (recordDate >= thirtyDaysAgo) {
        stats.recentActivity.last30Days.records++
        stats.recentActivity.last30Days.expenses += amount
      }
    })

    plans.forEach(plan => {
      const createDate = new Date(plan.createTime || plan.createdAt)
      
      if (createDate >= sevenDaysAgo) {
        stats.recentActivity.last7Days.plans++
      }
      
      if (createDate >= thirtyDaysAgo) {
        stats.recentActivity.last30Days.plans++
      }
    })

    // 计算活跃度评分 (0-100)
    const weeklyScore = Math.min(stats.recentActivity.last7Days.records * 10, 50)
    const planScore = Math.min(stats.recentActivity.last7Days.plans * 20, 30)
    const expenseScore = Math.min(stats.recentActivity.last7Days.expenses / 100, 20)
    
    stats.activityScore = Math.round(weeklyScore + planScore + expenseScore)

    // 计算连续使用天数（简化版）
    stats.streakDays = this.calculateStreakDays(records, plans)

    this.cacheResult(cacheKey, stats)
    return stats
  }

  /**
   * 生成趋势数据
   */
  generateTrendData(records) {
    const trendMap = new Map()
    
    records.forEach(record => {
      const date = new Date(record.date)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      if (!trendMap.has(monthKey)) {
        trendMap.set(monthKey, 0)
      }
      
      trendMap.set(monthKey, trendMap.get(monthKey) + (Number(record.amount) || 0))
    })
    
    return Array.from(trendMap.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .slice(-12) // 最近12个月
      .map(([month, amount]) => ({ month, amount }))
  }

  /**
   * 计算自第一条记录以来的天数
   */
  calculateDaysSinceFirstRecord(records) {
    if (!records || records.length === 0) return 0
    
    const dates = records.map(record => new Date(record.date)).sort((a, b) => a - b)
    const firstDate = dates[0]
    const now = new Date()
    
    return Math.ceil((now - firstDate) / (1000 * 60 * 60 * 24))
  }

  /**
   * 计算连续使用天数
   */
  calculateStreakDays(records, plans) {
    // 简化实现：基于最近记录的频率
    const recentRecords = records.filter(record => {
      const recordDate = new Date(record.date)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      return recordDate >= sevenDaysAgo
    })
    
    return Math.min(recentRecords.length, 7)
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(type, data, options) {
    const dataHash = this.simpleHash(JSON.stringify(data))
    const optionsHash = this.simpleHash(JSON.stringify(options))
    return `${type}_${dataHash}_${optionsHash}`
  }

  /**
   * 简单哈希函数
   */
  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 缓存计算结果
   */
  cacheResult(key, result) {
    // 检查缓存大小限制
    if (this.computationCache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.computationCache.keys().next().value
      this.computationCache.delete(firstKey)
    }
    
    this.computationCache.set(key, result)
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.computationCache.clear()
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    return {
      size: this.computationCache.size,
      maxSize: this.maxCacheSize,
      usage: `${(this.computationCache.size / this.maxCacheSize * 100).toFixed(1)}%`
    }
  }
}

// 创建全局实例
const localComputationEngine = new LocalComputationEngine()

export default localComputationEngine
