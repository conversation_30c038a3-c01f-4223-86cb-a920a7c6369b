/**
 * 智能调用调度器
 * 基于用户行为和数据重要性优化云函数调用时机
 */

import enhancedCacheManager from './enhanced-cache-manager.js'

class SmartCallScheduler {
  constructor() {
    // 调用优先级配置
    this.priorities = {
      CRITICAL: 0,    // 立即执行
      HIGH: 1000,     // 1秒内执行
      NORMAL: 5000,   // 5秒内执行
      LOW: 30000,     // 30秒内执行
      BACKGROUND: 300000 // 5分钟内执行
    }

    // 调用队列
    this.queues = {
      CRITICAL: [],
      HIGH: [],
      NORMAL: [],
      LOW: [],
      BACKGROUND: []
    }

    // 用户行为分析
    this.userBehavior = {
      isActive: true,
      lastActivity: Date.now(),
      activityPattern: 'normal', // normal, high, low
      currentPage: '',
      pageEnterTime: Date.now()
    }

    // 网络状态
    this.networkStatus = {
      type: 'wifi',
      isConnected: true,
      quality: 'good' // good, fair, poor
    }

    // 调度器状态
    this.isRunning = false
    this.schedulerTimer = null

    // 统计信息
    this.stats = {
      totalScheduled: 0,
      executed: 0,
      deferred: 0,
      cancelled: 0
    }
  }

  /**
   * 初始化调度器
   */
  init() {
    this.startScheduler()
    this.setupNetworkMonitoring()
    this.setupUserActivityMonitoring()
    console.log('智能调用调度器已启动')
  }

  /**
   * 调度云函数调用
   */
  schedule(callFunction, options = {}) {
    const {
      priority = 'NORMAL',
      context = {},
      retryCount = 0,
      maxRetries = 3,
      timeout = 10000,
      canDefer = true,
      requiresNetwork = true
    } = options

    const callItem = {
      id: this.generateId(),
      callFunction,
      priority,
      context,
      retryCount,
      maxRetries,
      timeout,
      canDefer,
      requiresNetwork,
      scheduledTime: Date.now(),
      executeAfter: Date.now() + this.priorities[priority]
    }

    // 根据当前状态决定是否立即执行或延迟
    const shouldDefer = this.shouldDeferCall(callItem)
    
    if (shouldDefer && canDefer) {
      this.addToQueue(callItem)
      this.stats.deferred++
      console.log(`调用已延迟调度: ${priority}, ID: ${callItem.id}`)
    } else {
      this.executeImmediately(callItem)
    }

    this.stats.totalScheduled++
    return callItem.id
  }

  /**
   * 判断是否应该延迟调用
   */
  shouldDeferCall(callItem) {
    // 关键调用不延迟
    if (callItem.priority === 'CRITICAL') {
      return false
    }

    // 网络状态不佳时延迟非关键调用
    if (this.networkStatus.quality === 'poor' && callItem.priority !== 'HIGH') {
      return true
    }

    // 用户不活跃时延迟低优先级调用
    if (!this.userBehavior.isActive && 
        (callItem.priority === 'LOW' || callItem.priority === 'BACKGROUND')) {
      return true
    }

    // 页面切换时延迟背景调用
    if (this.isPageTransitioning() && callItem.priority === 'BACKGROUND') {
      return true
    }

    return false
  }

  /**
   * 添加到队列
   */
  addToQueue(callItem) {
    const queue = this.queues[callItem.priority]
    if (queue) {
      queue.push(callItem)
      // 按执行时间排序
      queue.sort((a, b) => a.executeAfter - b.executeAfter)
    }
  }

  /**
   * 立即执行调用
   */
  async executeImmediately(callItem) {
    try {
      console.log(`立即执行调用: ${callItem.priority}, ID: ${callItem.id}`)
      const result = await this.executeCall(callItem)
      this.stats.executed++
      return result
    } catch (error) {
      console.error(`调用执行失败: ${callItem.id}`, error)
      
      // 重试逻辑
      if (callItem.retryCount < callItem.maxRetries) {
        callItem.retryCount++
        callItem.executeAfter = Date.now() + (callItem.retryCount * 2000) // 指数退避
        this.addToQueue(callItem)
      } else {
        this.stats.cancelled++
      }
    }
  }

  /**
   * 执行调用
   */
  async executeCall(callItem) {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('调用超时')), callItem.timeout)
    })

    return await Promise.race([
      callItem.callFunction(),
      timeoutPromise
    ])
  }

  /**
   * 启动调度器
   */
  startScheduler() {
    if (this.isRunning) return

    this.isRunning = true
    this.schedulerTimer = setInterval(() => {
      this.processQueues()
    }, 1000) // 每秒检查一次
  }

  /**
   * 处理队列
   */
  async processQueues() {
    const now = Date.now()
    
    // 按优先级处理队列
    for (const priority of Object.keys(this.queues)) {
      const queue = this.queues[priority]
      
      // 找到可以执行的调用
      const readyItems = queue.filter(item => now >= item.executeAfter)
      
      if (readyItems.length > 0) {
        // 从队列中移除
        readyItems.forEach(item => {
          const index = queue.indexOf(item)
          if (index > -1) {
            queue.splice(index, 1)
          }
        })

        // 并行执行（限制并发数）
        const maxConcurrent = this.getMaxConcurrent(priority)
        const batches = this.chunkArray(readyItems, maxConcurrent)
        
        for (const batch of batches) {
          await Promise.allSettled(
            batch.map(item => this.executeImmediately(item))
          )
        }
      }
    }
  }

  /**
   * 获取最大并发数
   */
  getMaxConcurrent(priority) {
    switch (priority) {
      case 'CRITICAL': return 3
      case 'HIGH': return 2
      case 'NORMAL': return 2
      case 'LOW': return 1
      case 'BACKGROUND': return 1
      default: return 1
    }
  }

  /**
   * 数组分块
   */
  chunkArray(array, chunkSize) {
    const chunks = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * 设置网络监控
   */
  setupNetworkMonitoring() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.networkStatus.isConnected = res.isConnected
      this.networkStatus.type = res.networkType
      
      // 简单的网络质量评估
      if (res.networkType === 'wifi') {
        this.networkStatus.quality = 'good'
      } else if (res.networkType === '4g') {
        this.networkStatus.quality = 'fair'
      } else {
        this.networkStatus.quality = 'poor'
      }

      console.log('网络状态变化:', this.networkStatus)
    })
  }

  /**
   * 设置用户活动监控
   */
  setupUserActivityMonitoring() {
    // 监听页面显示/隐藏（基于官方文档的正确用法）
    wx.onAppShow((options) => {
      this.userBehavior.isActive = true
      this.userBehavior.lastActivity = Date.now()
      console.log('应用切换到前台，用户活跃状态更新')
    })

    wx.onAppHide(() => {
      this.userBehavior.isActive = false
      console.log('应用切换到后台，用户非活跃状态')
    })

    // 定期检查用户活跃度（从30秒延长到60秒，减少检查频率）
    setInterval(() => {
      const inactiveTime = Date.now() - this.userBehavior.lastActivity
      if (inactiveTime > 60000) { // 1分钟无活动
        this.userBehavior.isActive = false
      }
    }, 60000) // 60秒
  }

  /**
   * 更新用户活动
   */
  updateUserActivity(page = '') {
    this.userBehavior.lastActivity = Date.now()
    this.userBehavior.isActive = true
    
    if (page && page !== this.userBehavior.currentPage) {
      this.userBehavior.currentPage = page
      this.userBehavior.pageEnterTime = Date.now()
    }
  }

  /**
   * 检查是否在页面切换中
   */
  isPageTransitioning() {
    const timeSincePageEnter = Date.now() - this.userBehavior.pageEnterTime
    return timeSincePageEnter < 2000 // 页面进入2秒内认为是切换中
  }

  /**
   * 取消调度的调用
   */
  cancel(callId) {
    for (const queue of Object.values(this.queues)) {
      const index = queue.findIndex(item => item.id === callId)
      if (index > -1) {
        queue.splice(index, 1)
        this.stats.cancelled++
        return true
      }
    }
    return false
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    const status = {}
    for (const [priority, queue] of Object.entries(this.queues)) {
      status[priority] = queue.length
    }
    return {
      queues: status,
      stats: this.stats,
      userBehavior: this.userBehavior,
      networkStatus: this.networkStatus
    }
  }

  /**
   * 停止调度器
   */
  stop() {
    if (this.schedulerTimer) {
      clearInterval(this.schedulerTimer)
      this.schedulerTimer = null
    }
    this.isRunning = false
    console.log('智能调用调度器已停止')
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Math.random().toString(36).substr(2, 9)
  }
}

// 创建全局实例
const smartCallScheduler = new SmartCallScheduler()

export default smartCallScheduler
