/**
 * 增强缓存管理器
 * 基于现有simple-data-manager优化，减少云函数调用
 */

class EnhancedCacheManager {
  constructor() {
    // 缓存配置
    this.config = {
      // 内存缓存限制
      maxMemoryItems: 100,
      maxMemorySize: 8 * 1024 * 1024, // 8MB
      
      // 本地存储限制
      maxLocalStorageSize: 8 * 1024 * 1024, // 8MB，为其他功能留2MB
      
      // TTL配置
      ttl: {
        critical: 30 * 1000,      // 30秒 - 关键数据
        important: 5 * 60 * 1000, // 5分钟 - 重要数据
        normal: 30 * 60 * 1000,   // 30分钟 - 一般数据
        statistics: 60 * 60 * 1000 // 1小时 - 统计数据
      }
    }
    
    // 内存缓存
    this.memoryCache = new Map()
    
    // 调用合并队列
    this.pendingCalls = new Map()
    
    // 统计信息
    this.stats = {
      memoryHits: 0,
      localHits: 0,
      cloudCalls: 0,
      mergedCalls: 0,
      totalRequests: 0
    }
  }

  /**
   * 智能获取数据 - 本地优先策略
   */
  async smartGet(key, cloudFunction, options = {}) {
    const {
      ttl = this.config.ttl.normal,
      priority = 'normal',
      forceRefresh = false,
      enableMerge = true
    } = options

    this.stats.totalRequests++

    try {
      // 1. 检查内存缓存
      if (!forceRefresh) {
        const memoryData = this.getFromMemory(key)
        if (memoryData) {
          this.stats.memoryHits++
          return { success: true, data: memoryData, source: 'memory' }
        }
      }

      // 2. 检查本地存储
      if (!forceRefresh) {
        const localData = this.getFromLocal(key)
        if (localData) {
          this.stats.localHits++
          // 同时更新内存缓存
          this.setToMemory(key, localData, ttl)
          return { success: true, data: localData, source: 'local' }
        }
      }

      // 3. 调用合并处理
      if (enableMerge && this.pendingCalls.has(key)) {
        this.stats.mergedCalls++
        return await this.pendingCalls.get(key)
      }

      // 4. 云端获取（仅当有有效的云函数时）
      if (cloudFunction && typeof cloudFunction === 'function') {
        const cloudPromise = this.fetchFromCloud(key, cloudFunction, ttl)
        if (enableMerge) {
          this.pendingCalls.set(key, cloudPromise)
        }

        const result = await cloudPromise

        if (enableMerge) {
          this.pendingCalls.delete(key)
        }

        return result
      }

      // 没有云函数时返回失败
      return { success: false, error: '没有可用的云函数' }

    } catch (error) {
      console.error(`智能获取数据失败 [${key}]:`, error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 从云端获取数据
   */
  async fetchFromCloud(key, cloudFunction, ttl) {
    this.stats.cloudCalls++

    try {
      // 检查cloudFunction是否为函数
      if (typeof cloudFunction !== 'function') {
        console.error('cloudFunction必须是一个函数:', typeof cloudFunction)
        return { success: false, error: 'cloudFunction必须是一个函数' }
      }

      const result = await cloudFunction()

      if (result && result.success && result.data) {
        // 缓存数据
        this.setToMemory(key, result.data, ttl)
        this.setToLocal(key, result.data, ttl)

        return { success: true, data: result.data, source: 'cloud' }
      }

      return result || { success: false, error: '云函数返回结果为空' }
    } catch (error) {
      console.error('云函数调用失败:', error)
      throw error
    }
  }

  /**
   * 内存缓存操作
   */
  getFromMemory(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null
    
    // 检查过期
    if (Date.now() > cached.expireTime) {
      this.memoryCache.delete(key)
      return null
    }
    
    // 更新访问计数
    cached.accessCount++
    return cached.data
  }

  setToMemory(key, data, ttl) {
    // 检查内存限制
    if (this.memoryCache.size >= this.config.maxMemoryItems) {
      this.cleanupMemoryCache()
    }
    
    this.memoryCache.set(key, {
      data: data,
      createTime: Date.now(),
      expireTime: Date.now() + ttl,
      accessCount: 0
    })
  }

  /**
   * 本地存储操作
   */
  getFromLocal(key) {
    try {
      const cached = wx.getStorageSync(`cache_${key}`)
      if (!cached) return null
      
      // 检查过期
      if (Date.now() > cached.expireTime) {
        wx.removeStorageSync(`cache_${key}`)
        return null
      }
      
      return cached.data
    } catch (error) {
      console.error('本地存储读取失败:', error)
      return null
    }
  }

  setToLocal(key, data, ttl) {
    try {
      // 检查存储空间
      const storageInfo = wx.getStorageInfoSync()
      if (storageInfo.currentSize > this.config.maxLocalStorageSize) {
        this.cleanupLocalStorage()
      }
      
      wx.setStorageSync(`cache_${key}`, {
        data: data,
        createTime: Date.now(),
        expireTime: Date.now() + ttl
      })
    } catch (error) {
      console.error('本地存储写入失败:', error)
      // 存储失败时清理空间后重试
      this.cleanupLocalStorage()
      try {
        wx.setStorageSync(`cache_${key}`, {
          data: data,
          createTime: Date.now(),
          expireTime: Date.now() + ttl
        })
      } catch (retryError) {
        console.error('本地存储重试失败:', retryError)
      }
    }
  }

  /**
   * 清理内存缓存
   */
  cleanupMemoryCache() {
    const entries = Array.from(this.memoryCache.entries())
    
    // 按访问次数和创建时间排序，删除最少使用的
    entries.sort((a, b) => {
      const aItem = a[1]
      const bItem = b[1]
      
      // 先按访问次数排序
      if (aItem.accessCount !== bItem.accessCount) {
        return aItem.accessCount - bItem.accessCount
      }
      
      // 再按创建时间排序
      return aItem.createTime - bItem.createTime
    })
    
    // 删除25%的缓存
    const deleteCount = Math.floor(entries.length * 0.25)
    for (let i = 0; i < deleteCount; i++) {
      this.memoryCache.delete(entries[i][0])
    }
    
    console.log(`内存缓存清理完成，删除${deleteCount}项`)
  }

  /**
   * 清理本地存储
   */
  cleanupLocalStorage() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const keys = storageInfo.keys.filter(key => key.startsWith('cache_'))

      // 获取所有缓存项的信息
      const cacheItems = keys.map(key => {
        try {
          const data = wx.getStorageSync(key)
          return {
            key: key,
            createTime: data.createTime || 0,
            expireTime: data.expireTime || 0,
            size: JSON.stringify(data).length
          }
        } catch (error) {
          return null
        }
      }).filter(item => item !== null)

      // 删除过期的缓存
      const now = Date.now()
      let deletedCount = 0

      cacheItems.forEach(item => {
        if (now > item.expireTime) {
          wx.removeStorageSync(item.key)
          deletedCount++
        }
      })

      // 如果还需要更多空间，删除最旧的缓存
      const remainingItems = cacheItems.filter(item => now <= item.expireTime)
      if (remainingItems.length > 0) {
        remainingItems.sort((a, b) => a.createTime - b.createTime)

        const additionalDeleteCount = Math.min(10, remainingItems.length)
        for (let i = 0; i < additionalDeleteCount; i++) {
          wx.removeStorageSync(remainingItems[i].key)
          deletedCount++
        }
      }

      console.log(`本地存储清理完成，删除${deletedCount}项`)
    } catch (error) {
      console.error('本地存储清理失败:', error)
    }
  }

  /**
   * 批量操作管理器
   */
  createBatchManager() {
    return new BatchOperationManager()
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const memoryUsage = this.memoryCache.size
    const cacheHitRate = this.stats.totalRequests > 0 ?
      ((this.stats.memoryHits + this.stats.localHits) / this.stats.totalRequests * 100).toFixed(2) : 0

    return {
      ...this.stats,
      memoryUsage,
      cacheHitRate: `${cacheHitRate}%`,
      cloudCallReduction: this.stats.totalRequests > 0 ?
        ((this.stats.totalRequests - this.stats.cloudCalls) / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
    }
  }

  /**
   * 清理所有缓存
   */
  clearAll() {
    this.memoryCache.clear()

    try {
      const storageInfo = wx.getStorageInfoSync()
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'))
      cacheKeys.forEach(key => {
        wx.removeStorageSync(key)
      })
      console.log(`清理完成，删除${cacheKeys.length}个本地缓存项`)
    } catch (error) {
      console.error('清理本地缓存失败:', error)
    }
  }
}

/**
 * 批量操作管理器
 */
class BatchOperationManager {
  constructor() {
    this.queues = new Map()
    this.config = {
      maxBatchSize: 10,
      flushInterval: 3000, // 3秒自动提交
      maxWaitTime: 10000   // 最大等待10秒
    }
    this.timers = new Map()
  }

  /**
   * 添加操作到批量队列
   */
  addOperation(type, operation) {
    if (!this.queues.has(type)) {
      this.queues.set(type, [])
    }

    const queue = this.queues.get(type)
    queue.push({
      ...operation,
      id: this.generateId(),
      timestamp: Date.now()
    })

    // 检查是否需要立即执行
    if (queue.length >= this.config.maxBatchSize) {
      this.flushQueue(type)
    } else {
      this.scheduleFlush(type)
    }

    return operation.id || this.generateId()
  }

  /**
   * 调度队列刷新
   */
  scheduleFlush(type) {
    if (this.timers.has(type)) {
      clearTimeout(this.timers.get(type))
    }

    const timer = setTimeout(() => {
      this.flushQueue(type)
    }, this.config.flushInterval)

    this.timers.set(type, timer)
  }

  /**
   * 刷新队列
   */
  async flushQueue(type) {
    const queue = this.queues.get(type)
    if (!queue || queue.length === 0) return

    // 取出一批操作
    const batch = queue.splice(0, this.config.maxBatchSize)

    try {
      await this.executeBatch(type, batch)
      console.log(`批量操作完成: ${type}, 数量: ${batch.length}`)
    } catch (error) {
      console.error(`批量操作失败: ${type}`, error)
      // 可以选择重新加入队列或记录失败
    }

    // 清理定时器
    if (this.timers.has(type)) {
      clearTimeout(this.timers.get(type))
      this.timers.delete(type)
    }

    // 如果还有操作，继续处理
    if (queue.length > 0) {
      this.scheduleFlush(type)
    }
  }

  /**
   * 执行批量操作
   */
  async executeBatch(type, operations) {
    switch (type) {
      case 'travel_sync':
        return await this.batchTravelSync(operations)
      case 'expense_update':
        return await this.batchExpenseUpdate(operations)
      case 'statistics_update':
        return await this.batchStatisticsUpdate(operations)
      default:
        throw new Error(`未知批量操作类型: ${type}`)
    }
  }

  async batchTravelSync(operations) {
    return await wx.cloud.callFunction({
      name: 'travel',
      data: {
        action: 'batchSync',
        operations: operations
      }
    })
  }

  async batchExpenseUpdate(operations) {
    return await wx.cloud.callFunction({
      name: 'expense',
      data: {
        action: 'batchUpdate',
        operations: operations
      }
    })
  }

  async batchStatisticsUpdate(operations) {
    return await wx.cloud.callFunction({
      name: 'statistics',
      data: {
        action: 'batchUpdate',
        operations: operations
      }
    })
  }

  generateId() {
    return Math.random().toString(36).substr(2, 9)
  }
}

// 创建全局实例
const enhancedCacheManager = new EnhancedCacheManager()

export default enhancedCacheManager
