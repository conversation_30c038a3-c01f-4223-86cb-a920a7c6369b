/**
 * 统一性能管理器 - 整合所有性能优化功能
 * 解决performance-helper.js和performance-optimizer.js重复问题
 */

class UnifiedPerformanceManager {
  constructor() {
    // 批量setData队列
    this.setDataQueue = new Map()
    
    // 批量加载队列
    this.loadingQueue = new Map()
    
    // 防抖定时器
    this.debounceTimers = new Map()
    
    // 性能监控数据
    this.performanceData = {
      setDataCalls: 0,
      batchedCalls: 0,
      totalDataSize: 0,
      batchLoadCount: 0,
      cacheHitCount: 0,
      cacheMissCount: 0,
      totalLoadTime: 0,
      averageLoadTime: 0
    }
    
    // 配置
    this.config = {
      batchDelay: 16,           // 批量处理延迟（一帧时间）
      maxBatchSize: 10,         // 最大批量大小
      setDataDelay: 32,         // setData批量延迟
      maxRetries: 3,            // 最大重试次数
      retryDelay: 1000          // 重试延迟
    }
  }

  // ==================== 批量setData优化 ====================

  /**
   * 智能批量setData - 将多个setData调用合并为一次
   * @param {object} pageInstance 页面实例
   * @param {object} data 要设置的数据
   * @param {function} callback 回调函数
   * @param {number} delay 延迟时间(ms)，默认16ms(一帧)
   */
  smartSetData(pageInstance, data, callback = null, delay = 16) {
    if (!pageInstance || !pageInstance.setData) {
      console.error('无效的页面实例')
      return
    }

    const pageId = this.getPageId(pageInstance)
    
    // 合并数据到队列
    if (!this.setDataQueue.has(pageId)) {
      this.setDataQueue.set(pageId, {
        pageInstance,
        data: {},
        callbacks: []
      })
    }

    const queueItem = this.setDataQueue.get(pageId)
    Object.assign(queueItem.data, data)
    
    if (callback) {
      queueItem.callbacks.push(callback)
    }

    // 清除之前的定时器
    if (this.debounceTimers.has(pageId)) {
      clearTimeout(this.debounceTimers.get(pageId))
    }

    // 设置新的定时器
    this.debounceTimers.set(pageId, setTimeout(() => {
      this.flushSetData(pageId)
    }, delay))
  }

  /**
   * 立即执行批量setData
   * @param {string} pageId 页面ID
   */
  flushSetData(pageId) {
    const queueItem = this.setDataQueue.get(pageId)
    
    if (!queueItem || Object.keys(queueItem.data).length === 0) {
      return
    }

    try {
      // 执行setData
      queueItem.pageInstance.setData(queueItem.data, () => {
        // 执行所有回调
        queueItem.callbacks.forEach(callback => {
          try {
            callback()
          } catch (error) {
            console.error('setData回调执行失败:', error)
          }
        })
      })

      // 更新性能统计
      this.performanceData.setDataCalls++
      this.performanceData.batchedCalls++
      this.performanceData.totalDataSize += this.calculateDataSize(queueItem.data)

    } catch (error) {
      console.error('批量setData执行失败:', error)
    } finally {
      // 清理队列
      this.setDataQueue.delete(pageId)
      this.debounceTimers.delete(pageId)
    }
  }

  // ==================== 批量数据加载优化 ====================

  /**
   * 批量数据加载
   * @param {Array} requests 请求数组
   * @param {Object} options 选项
   */
  async batchLoad(requests, options = {}) {
    const startTime = Date.now()
    const { 
      maxConcurrency = 3,
      timeout = 5000,
      retries = this.config.maxRetries 
    } = options

    try {
      // 分批处理请求
      const batches = this.chunkArray(requests, maxConcurrency)
      const results = []

      for (const batch of batches) {
        const batchPromises = batch.map(request => 
          this.executeRequest(request, { timeout, retries })
        )
        
        const batchResults = await Promise.allSettled(batchPromises)
        results.push(...batchResults)
      }

      // 更新性能统计
      const loadTime = Date.now() - startTime
      this.performanceData.batchLoadCount++
      this.performanceData.totalLoadTime += loadTime
      this.performanceData.averageLoadTime = 
        this.performanceData.totalLoadTime / this.performanceData.batchLoadCount

      return {
        success: true,
        results: results.map(r => r.status === 'fulfilled' ? r.value : r.reason),
        loadTime
      }
    } catch (error) {
      console.error('批量加载失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 执行单个请求（带重试）
   */
  async executeRequest(request, options) {
    const { timeout, retries } = options
    let lastError

    for (let i = 0; i <= retries; i++) {
      try {
        return await Promise.race([
          request(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('请求超时')), timeout)
          )
        ])
      } catch (error) {
        lastError = error
        if (i < retries) {
          await new Promise(resolve => 
            setTimeout(resolve, this.config.retryDelay * (i + 1))
          )
        }
      }
    }

    throw lastError
  }

  // ==================== 防抖和节流 ====================

  /**
   * 防抖函数
   * @param {function} func 要防抖的函数
   * @param {number} delay 延迟时间
   * @param {string} key 防抖键
   */
  debounce(func, delay, key = 'default') {
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key))
    }

    this.debounceTimers.set(key, setTimeout(() => {
      func()
      this.debounceTimers.delete(key)
    }, delay))
  }

  /**
   * 节流函数
   * @param {function} func 要节流的函数
   * @param {number} interval 间隔时间
   * @param {string} key 节流键
   */
  throttle(func, interval, key = 'default') {
    const throttleKey = `throttle_${key}`
    
    if (!this.debounceTimers.has(throttleKey)) {
      func()
      this.debounceTimers.set(throttleKey, setTimeout(() => {
        this.debounceTimers.delete(throttleKey)
      }, interval))
    }
  }

  // ==================== 智能缓存策略 ====================

  /**
   * 智能缓存策略（增强版）
   * @param {string} dataType 数据类型
   * @param {number} lastUpdate 最后更新时间
   * @param {Object} options 选项
   */
  shouldUseCache(dataType, lastUpdate, options = {}) {
    const { forceRefresh = false, maxAge = 300000, priority = 'normal' } = options

    if (forceRefresh) {
      this.performanceData.cacheMissCount++
      return false
    }

    // 动态缓存规则（根据使用频率调整）
    const baseCacheRules = {
      'user_info': 300000,      // 5分钟
      'travel_plans': 60000,    // 1分钟
      'expense_records': 0,     // 不缓存
      'analytics': 180000,      // 3分钟
      'current_plan': 30000,    // 30秒
      'ongoing_plans': 60000    // 1分钟
    }

    // 根据优先级调整缓存时间
    const priorityMultiplier = {
      'high': 1.5,      // 高优先级数据缓存时间延长50%
      'normal': 1.0,    // 正常
      'low': 0.7        // 低优先级数据缓存时间缩短30%
    }

    const multiplier = priorityMultiplier[priority] || 1.0
    const ttl = (baseCacheRules[dataType] || maxAge) * multiplier
    const shouldCache = Date.now() - lastUpdate < ttl

    if (shouldCache) {
      this.performanceData.cacheHitCount++
    } else {
      this.performanceData.cacheMissCount++
    }

    return shouldCache
  }

  /**
   * 预测性缓存预加载
   * @param {Array} dataTypes 需要预加载的数据类型
   */
  async predictivePreload(dataTypes) {
    const preloadPromises = dataTypes.map(async (dataType) => {
      try {
        // 根据数据类型预加载相应数据
        switch (dataType) {
          case 'user_info':
            // 预加载用户信息
            break
          case 'travel_plans':
            // 预加载旅行计划
            break
          case 'financial_overview':
            // 预加载财务概览
            break
        }
      } catch (error) {
        console.warn(`预加载 ${dataType} 失败:`, error)
      }
    })

    await Promise.allSettled(preloadPromises)
  }

  // ==================== 工具方法 ====================

  /**
   * 获取页面ID
   */
  getPageId(pageInstance) {
    return pageInstance.route || pageInstance.__route__ || 'unknown'
  }

  /**
   * 计算数据大小
   */
  calculateDataSize(data) {
    try {
      return JSON.stringify(data).length
    } catch (error) {
      return 0
    }
  }

  /**
   * 数组分块
   */
  chunkArray(array, size) {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * 检查数据是否发生变化
   */
  isDataChanged(oldValue, newValue) {
    try {
      return JSON.stringify(oldValue) !== JSON.stringify(newValue)
    } catch (error) {
      return true
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performanceData,
      cacheHitRate: this.performanceData.cacheHitCount / 
        (this.performanceData.cacheHitCount + this.performanceData.cacheMissCount) * 100,
      queueSizes: {
        setData: this.setDataQueue.size,
        loading: this.loadingQueue.size,
        timers: this.debounceTimers.size
      }
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清理所有定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    
    // 清理队列
    this.setDataQueue.clear()
    this.loadingQueue.clear()
    
    // 只在开发环境输出清理日志
    if (typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
      console.log('性能管理器资源清理完成')
    }
  }
}

// 创建全局实例
const unifiedPerformanceManager = new UnifiedPerformanceManager()

export default unifiedPerformanceManager
