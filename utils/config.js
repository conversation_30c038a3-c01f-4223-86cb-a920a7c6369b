/**
 * 应用配置文件
 * 控制调试模式和生产环境设置
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
}

// 当前环境（发布时改为 PRODUCTION）
const CURRENT_ENV = ENV.DEVELOPMENT

// 配置项
const CONFIG = {
  // 调试模式
  DEBUG: CURRENT_ENV === ENV.DEVELOPMENT,
  
  // 日志级别
  LOG_LEVEL: CURRENT_ENV === ENV.DEVELOPMENT ? 'debug' : 'error',
  
  // API配置
  API: {
    timeout: 10000,
    retryCount: 3
  },
  
  // 云开发配置
  CLOUD: {
    env: CURRENT_ENV === ENV.DEVELOPMENT ? 'dev-env' : 'prod-env'
  },
  
  // 缓存配置
  CACHE: {
    expireTime: 24 * 60 * 60 * 1000, // 24小时
    maxSize: 100
  }
}

// 日志工具 - 完整实现
const Logger = {
  // 日志队列
  logQueue: [],
  maxQueueSize: 100,

  debug: function(...args) {
    // 只在开发环境输出debug日志
    if (CONFIG.DEBUG && typeof __wxConfig !== 'undefined' && __wxConfig.debug) {
      console.log('[DEBUG]', new Date().toISOString(), ...args)
      this.addToQueue('debug', args)
    }
  },

  info: function(...args) {
    // 减少info日志的输出频率
    if (Math.random() < 0.1) { // 只输出10%的info日志
      console.log('[INFO]', new Date().toISOString(), ...args)
      this.addToQueue('info', args)
    }
  },

  warn: function(...args) {
    console.warn('[WARN]', new Date().toISOString(), ...args)
    this.addToQueue('warn', args)
  },

  error: function(...args) {
    console.error('[ERROR]', new Date().toISOString(), ...args)
    this.addToQueue('error', args)
    this.uploadErrorImmediately(args)
  },

  addToQueue: function(level, args) {
    if (this.logQueue.length >= this.maxQueueSize) {
      this.logQueue.shift() // 移除最旧的日志
    }

    this.logQueue.push({
      level,
      message: args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' '),
      timestamp: Date.now(),
      page: this.getCurrentPage()
    })
  },

  getCurrentPage: function() {
    try {
      const pages = getCurrentPages()
      return pages.length > 0 ? pages[pages.length - 1].route : 'unknown'
    } catch (error) {
      return 'unknown'
    }
  },

  uploadErrorImmediately: function(args) {
    // 立即上报严重错误
    try {
      wx.cloud.callFunction({
        name: 'system',
        data: {
          action: 'reportError',
          error: {
            message: args.join(' '),
            timestamp: Date.now(),
            page: this.getCurrentPage(),
            userAgent: wx.getSystemInfoSync().platform
          }
        }
      }).catch(() => {
        // 静默处理上报失败
      })
    } catch (error) {
      // 静默处理
    }
  }
}

export { CONFIG, Logger, ENV }
export default CONFIG
