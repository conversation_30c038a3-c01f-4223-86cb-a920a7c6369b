/**
 * 分层数据同步管理器
 * 关键数据：立即同步（支付、删除等）
 * 重要数据：延迟同步（编辑、添加等）
 * 一般数据：批量同步（统计、展示等）
 */

import optimisticUpdateManager from './optimistic-update-manager.js'

class LayeredSyncManager {
  constructor() {
    // 同步队列分层
    this.criticalQueue = new Map()    // 关键数据队列
    this.importantQueue = new Map()   // 重要数据队列
    this.generalQueue = new Map()     // 一般数据队列
    
    // 配置
    this.config = {
      // 关键数据：立即同步
      critical: {
        syncDelay: 0,
        maxRetries: 5,
        timeout: 8000
      },
      // 重要数据：3秒延迟同步
      important: {
        syncDelay: 3000,
        maxRetries: 3,
        timeout: 10000
      },
      // 一般数据：30秒批量同步
      general: {
        syncDelay: 30000,
        maxRetries: 2,
        timeout: 15000
      }
    }
    
    // 数据分类定义
    this.dataCategories = {
      critical: [
        'deleteRecord',
        'deletePlan',
        'payment',
        'refund',
        'joinCollaboration',
        'leaveCollaboration'
      ],
      important: [
        'addRecord',
        'updateRecord',
        'addPlan',
        'updatePlan',
        'updateUserInfo'
      ],
      general: [
        'updateStatistics',
        'updatePreferences',
        'logActivity',
        'updateCache'
      ]
    }
    
    // 启动定时同步
    this.startScheduledSync()
  }

  /**
   * 分层同步数据
   * @param {string} action 操作类型
   * @param {object} data 数据
   * @param {function} syncFn 同步函数
   * @param {function} uiUpdateFn UI更新函数
   * @param {object} options 选项
   */
  async layeredSync(action, data, syncFn, uiUpdateFn, options = {}) {
    const category = this.categorizeAction(action)
    const syncConfig = this.config[category]
    
    // 使用乐观更新管理器
    const result = await optimisticUpdateManager.optimisticUpdate(
      action,
      data,
      uiUpdateFn,
      syncFn,
      {
        critical: category === 'critical',
        ...options
      }
    )
    
    // 根据分类处理同步
    switch (category) {
      case 'critical':
        // 关键数据立即同步（已在乐观更新中处理）
        break
        
      case 'important':
        // 重要数据延迟同步
        this.scheduleImportantSync(action, data, syncFn, syncConfig.syncDelay)
        break
        
      case 'general':
        // 一般数据加入批量同步队列
        this.addToGeneralQueue(action, data, syncFn)
        break
    }
    
    return result
  }

  /**
   * 分类操作类型
   */
  categorizeAction(action) {
    for (const [category, actions] of Object.entries(this.dataCategories)) {
      if (actions.includes(action)) {
        return category
      }
    }
    return 'general' // 默认为一般数据
  }

  /**
   * 调度重要数据同步
   */
  scheduleImportantSync(action, data, syncFn, delay) {
    const syncId = `${action}_${Date.now()}`
    
    setTimeout(async () => {
      try {
        await syncFn(data)
        console.log(`重要数据同步成功: ${action}`)
      } catch (error) {
        console.error(`重要数据同步失败: ${action}`, error)
        // 重要数据失败时，降级为一般数据重试
        this.addToGeneralQueue(action, data, syncFn)
      }
    }, delay)
  }

  /**
   * 添加到一般数据队列
   */
  addToGeneralQueue(action, data, syncFn) {
    const queueId = `${action}_${Date.now()}`
    this.generalQueue.set(queueId, {
      action,
      data,
      syncFn,
      timestamp: Date.now()
    })
  }

  /**
   * 启动定时同步（优化频率，减少与WebSocket冲突）
   */
  startScheduledSync() {
    // 一般数据批量同步（从30秒延长到2分钟，减少频率）
    setInterval(() => {
      this.batchSyncGeneral()
    }, 2 * 60 * 1000) // 2分钟

    // 清理过期队列（从5分钟延长到10分钟）
    setInterval(() => {
      this.cleanupQueues()
    }, 10 * 60 * 1000) // 10分钟
  }

  /**
   * 批量同步一般数据
   */
  async batchSyncGeneral() {
    if (this.generalQueue.size === 0) return
    
    console.log(`开始批量同步一般数据，队列大小: ${this.generalQueue.size}`)
    
    const operations = Array.from(this.generalQueue.entries())
    this.generalQueue.clear()
    
    // 按操作类型分组
    const groupedOps = this.groupOperations(operations)
    
    // 并行处理各组
    const syncPromises = Object.entries(groupedOps).map(([action, ops]) => 
      this.syncOperationGroup(action, ops)
    )
    
    await Promise.allSettled(syncPromises)
  }

  /**
   * 按操作类型分组
   */
  groupOperations(operations) {
    const groups = {}
    
    operations.forEach(([queueId, operation]) => {
      const { action } = operation
      if (!groups[action]) {
        groups[action] = []
      }
      groups[action].push(operation)
    })
    
    return groups
  }

  /**
   * 同步操作组
   */
  async syncOperationGroup(action, operations) {
    try {
      // 对于相同操作，只保留最新的数据
      const latestOp = operations.sort((a, b) => b.timestamp - a.timestamp)[0]
      
      await latestOp.syncFn(latestOp.data)
      console.log(`操作组同步成功: ${action}, 操作数: ${operations.length}`)
      
    } catch (error) {
      console.error(`操作组同步失败: ${action}`, error)
      
      // 失败的操作重新加入队列（最多重试一次）
      operations.forEach(op => {
        if (!op.retried) {
          op.retried = true
          this.addToGeneralQueue(op.action, op.data, op.syncFn)
        }
      })
    }
  }

  /**
   * 清理过期队列
   */
  cleanupQueues() {
    const now = Date.now()
    const maxAge = 60 * 60 * 1000 // 1小时
    
    // 清理一般数据队列
    for (const [queueId, operation] of this.generalQueue.entries()) {
      if (now - operation.timestamp > maxAge) {
        this.generalQueue.delete(queueId)
      }
    }
    
    console.log(`队列清理完成，剩余一般数据: ${this.generalQueue.size}`)
  }

  /**
   * 强制同步所有队列
   */
  async forceSync() {
    console.log('开始强制同步所有队列')
    
    // 立即同步一般数据
    await this.batchSyncGeneral()
    
    // 等待乐观更新管理器完成
    const status = optimisticUpdateManager.getSyncStatus()
    if (status.queueSize > 0) {
      console.log(`等待乐观更新完成，队列大小: ${status.queueSize}`)
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    const optimisticStatus = optimisticUpdateManager.getSyncStatus()
    
    return {
      critical: optimisticStatus.queueSize,
      important: 0, // 重要数据使用定时器，无队列
      general: this.generalQueue.size,
      total: optimisticStatus.queueSize + this.generalQueue.size,
      lastSync: optimisticStatus.lastSyncTime
    }
  }

  /**
   * 设置数据分类
   */
  setDataCategory(action, category) {
    // 从其他分类中移除
    Object.values(this.dataCategories).forEach(actions => {
      const index = actions.indexOf(action)
      if (index > -1) {
        actions.splice(index, 1)
      }
    })
    
    // 添加到新分类
    if (this.dataCategories[category]) {
      this.dataCategories[category].push(action)
    }
  }
}

// 创建全局实例
const layeredSyncManager = new LayeredSyncManager()

export default layeredSyncManager
export { LayeredSyncManager }
