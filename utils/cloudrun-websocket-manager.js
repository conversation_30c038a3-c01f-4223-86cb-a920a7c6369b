/**
 * 微信云托管WebSocket管理器
 * 基于官方wx.cloud.connectContainer API实现
 * 用于协作功能的实时数据同步
 */

class CloudRunWebSocketManager {
  constructor() {
    // WebSocket连接实例
    this.socketTask = null
    
    // 连接状态
    this.isConnected = false
    this.isConnecting = false
    
    // 配置参数
    this.config = {
      env: '', // 云托管环境ID
      service: '', // 服务名
      path: '/', // 路径
      timeout: 10000, // 10秒超时
      reconnectInterval: 3000, // 重连间隔
      maxReconnectAttempts: 5, // 最大重连次数
      heartbeatInterval: 30000 // 心跳间隔30秒
    }
    
    // 重连状态
    this.reconnectAttempts = 0
    this.reconnectTimer = null
    
    // 心跳状态
    this.heartbeatTimer = null
    this.lastHeartbeatTime = 0
    
    // 消息队列（离线时缓存）
    this.messageQueue = []
    this.maxQueueSize = 100
    
    // 事件监听器
    this.listeners = new Map()
    
    // 统计信息
    this.stats = {
      totalMessages: 0,
      sentMessages: 0,
      receivedMessages: 0,
      reconnectCount: 0,
      lastConnectTime: 0,
      connectionDuration: 0
    }
  }

  /**
   * 初始化云托管WebSocket连接
   */
  async init(env, service, options = {}) {
    if (this.isConnecting || this.isConnected) {
      console.warn('云托管WebSocket已连接或正在连接中')
      return false
    }

    // 更新配置
    this.config = { ...this.config, ...options }
    this.config.env = env
    this.config.service = service

    try {
      await this.connect()
      return true
    } catch (error) {
      console.error('云托管WebSocket初始化失败:', error)
      return false
    }
  }

  /**
   * 建立云托管WebSocket连接（优先使用connectContainer）
   */
  async connect() {
    if (this.isConnecting) return

    this.isConnecting = true

    try {
      // 优先尝试使用微信云托管的connectContainer方法
      if (wx.connectContainer) {
        console.log('使用wx.connectContainer连接云托管WebSocket服务')

        this.socketTask = wx.connectContainer({
          path: this.config.path,
          success: () => {
            console.log('云托管WebSocket连接请求发送成功')
          },
          fail: (error) => {
            console.error('云托管WebSocket连接请求失败:', error)
            console.error('失败详情:', JSON.stringify(error, null, 2))
            this.isConnecting = false
            // 回退到传统WebSocket连接
            this.connectWithTraditionalWebSocket()
            return
          }
        })
      } else {
        // 回退到传统WebSocket连接
        console.log('wx.connectContainer不可用，使用传统WebSocket连接')
        this.connectWithTraditionalWebSocket()
        return
      }

      // 设置事件监听器
      this.setupEventListeners()

    } catch (error) {
      console.error('WebSocket连接异常:', error)
      this.isConnecting = false
      // 回退到传统WebSocket连接
      this.connectWithTraditionalWebSocket()
    }
  }

  /**
   * 传统WebSocket连接方法
   */
  connectWithTraditionalWebSocket() {
    try {
      // 构建WebSocket URL（使用实际生效的域名）
      const wsUrl = `wss://websocket-service-177711-9-1370752426.sh.run.tcloudbase.com${this.config.path}`
      console.log('使用传统WebSocket连接:', wsUrl)
      console.log('连接配置:', {
        timeout: this.config.timeout,
        protocols: ['collaboration-protocol'],
        reconnectAttempts: this.reconnectAttempts
      })

      this.socketTask = wx.connectSocket({
        url: wsUrl,
        protocols: ['collaboration-protocol'],
        timeout: this.config.timeout,
        success: () => {
          console.log('传统WebSocket连接请求发送成功')
        },
        fail: (error) => {
          console.error('传统WebSocket连接请求失败:', error)
          console.error('失败详情:', JSON.stringify(error, null, 2))
          this.isConnecting = false
          this.handleConnectionError(error)
        }
      })

      // 设置事件监听器
      this.setupEventListeners()

    } catch (error) {
      console.error('传统WebSocket连接异常:', error)
      this.isConnecting = false
      this.handleConnectionError(error)
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.socketTask) return

    // 连接打开
    this.socketTask.onOpen(() => {
      console.log('云托管WebSocket连接已建立')
      this.isConnected = true
      this.isConnecting = false
      this.reconnectAttempts = 0
      this.stats.lastConnectTime = Date.now()
      
      // 开始心跳
      this.startHeartbeat()
      
      // 处理离线消息队列
      this.processMessageQueue()
      
      // 触发连接成功事件
      this.emit('connected')
    })

    // 接收消息
    this.socketTask.onMessage((res) => {
      this.handleMessage(res.data)
    })

    // 连接关闭
    this.socketTask.onClose((res) => {
      console.log('云托管WebSocket连接已关闭:', res.code, res.reason)
      this.handleDisconnection()
    })

    // 连接错误
    this.socketTask.onError((error) => {
      console.error('云托管WebSocket连接错误:', error)
      console.error('错误详情:', JSON.stringify(error, null, 2))
      this.handleConnectionError(error)
    })
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    this.stats.receivedMessages++
    this.stats.totalMessages++

    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data
      
      // 处理心跳响应
      if (message.type === 'heartbeat') {
        this.lastHeartbeatTime = Date.now()
        return
      }

      // 触发消息事件
      this.emit('message', message)
      
      // 根据消息类型分发
      if (message.type) {
        this.emit(message.type, message.data)
      }

    } catch (error) {
      console.error('解析云托管WebSocket消息失败:', error, data)
    }
  }

  /**
   * 发送消息
   */
  send(data, type = 'data') {
    const message = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateMessageId()
    }

    if (this.isConnected && this.socketTask) {
      try {
        this.socketTask.send({
          data: JSON.stringify(message),
          success: () => {
            this.stats.sentMessages++
            this.stats.totalMessages++
            console.log('云托管消息发送成功:', message.id)
          },
          fail: (error) => {
            console.error('云托管消息发送失败:', error)
            // 发送失败时加入队列
            this.addToQueue(message)
          }
        })
      } catch (error) {
        console.error('发送云托管消息异常:', error)
        this.addToQueue(message)
      }
    } else {
      // 连接断开时加入队列
      this.addToQueue(message)
      console.log('连接断开，消息已加入队列:', message.id)
    }

    return message.id
  }

  /**
   * 添加消息到队列
   */
  addToQueue(message) {
    if (this.messageQueue.length >= this.maxQueueSize) {
      // 队列满时移除最旧的消息
      this.messageQueue.shift()
    }
    this.messageQueue.push(message)
  }

  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0) return

    console.log(`处理离线消息队列，共${this.messageQueue.length}条消息`)
    
    const messages = [...this.messageQueue]
    this.messageQueue = []

    messages.forEach(message => {
      this.socketTask.send({
        data: JSON.stringify(message),
        success: () => {
          this.stats.sentMessages++
          console.log('队列消息发送成功:', message.id)
        },
        fail: (error) => {
          console.error('队列消息发送失败:', error)
          // 重新加入队列
          this.addToQueue(message)
        }
      })
    })
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat() // 先停止之前的心跳
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ ping: Date.now() }, 'heartbeat')
        
        // 检查心跳超时
        const now = Date.now()
        if (this.lastHeartbeatTime > 0 && 
            now - this.lastHeartbeatTime > this.config.heartbeatInterval * 2) {
          console.warn('心跳超时，重新连接')
          this.reconnect()
        }
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 处理连接断开
   */
  handleDisconnection() {
    this.isConnected = false
    this.isConnecting = false
    this.stopHeartbeat()
    
    // 更新连接时长统计
    if (this.stats.lastConnectTime > 0) {
      this.stats.connectionDuration += Date.now() - this.stats.lastConnectTime
    }
    
    // 触发断开事件
    this.emit('disconnected')
    
    // 自动重连
    this.scheduleReconnect()
  }

  /**
   * 处理连接错误
   */
  handleConnectionError(error) {
    console.error('WebSocket连接错误处理:', error)
    this.isConnected = false
    this.isConnecting = false
    this.stopHeartbeat()

    // 触发错误事件
    this.emit('error', error)

    // 自动重连
    console.log('开始调度重连...')
    this.scheduleReconnect()
  }

  /**
   * 调度重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连')
      this.emit('maxReconnectReached')
      return
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    const delay = this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts)
    console.log(`${delay}ms后尝试重连 (${this.reconnectAttempts + 1}/${this.config.maxReconnectAttempts})`)

    this.reconnectTimer = setTimeout(() => {
      this.reconnect()
    }, delay)
  }

  /**
   * 重新连接
   */
  async reconnect() {
    if (this.isConnecting || this.isConnected) return

    this.reconnectAttempts++
    this.stats.reconnectCount++
    
    console.log(`尝试重连 ${this.reconnectAttempts}/${this.config.maxReconnectAttempts}`)
    
    try {
      await this.connect()
    } catch (error) {
      console.error('重连失败:', error)
    }
  }

  /**
   * 手动重连
   */
  async manualReconnect() {
    this.reconnectAttempts = 0
    await this.reconnect()
  }

  /**
   * 关闭连接
   */
  close(code = 1000, reason = '正常关闭') {
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.socketTask && this.isConnected) {
      this.socketTask.close({
        code,
        reason,
        success: () => {
          console.log('云托管WebSocket连接已主动关闭')
        }
      })
    }

    this.isConnected = false
    this.isConnecting = false
    this.socketTask = null
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件处理器执行错误:', error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      queueSize: this.messageQueue.length,
      stats: this.stats,
      config: {
        env: this.config.env,
        service: this.config.service,
        path: this.config.path
      }
    }
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 创建全局实例
const cloudRunWebSocketManager = new CloudRunWebSocketManager()

export default cloudRunWebSocketManager
