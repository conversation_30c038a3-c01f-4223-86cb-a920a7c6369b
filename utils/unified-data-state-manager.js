/**
 * 统一数据状态管理器
 * 解决多个数据管理器之间的数据同步和一致性问题
 * 采用观察者模式和单向数据流设计
 */

class UnifiedDataStateManager {
  constructor() {
    // 统一存储键名映射
    this.storageKeyMap = {
      // 记账相关
      expenseRecords: 'expense_records',           // 主数据
      expenseRecordsCache: 'expense_records_cache', // 视图缓存（兼容现有代码）
      financialOverview: 'financial_overview',      // 财务概览
      
      // 旅行相关
      travelPlans: 'travel_plans',
      travelPlansCache: 'travel_plans_cache',
      travelOverview: 'travel_overview',
      
      // WebSocket相关
      wsExpenseRecords: 'ws_expense_records',       // WebSocket本地记录
      wsSyncQueue: 'ws_sync_queue',                 // 同步队列
      wsSyncStatus: 'ws_sync_status'                // 同步状态
    }

    // 数据观察者
    this.observers = new Map()
    
    // 数据适配器
    this.adapters = new Map()
    
    // 数据状态
    this.dataState = new Map()
    
    // 同步状态
    this.syncStatus = {
      isOnline: false,
      lastSyncTime: 0,
      pendingOperations: 0
    }

    // 事件总线
    this.eventBus = {
      listeners: new Map(),
      emit: (event, data) => {
        const listeners = this.eventBus.listeners.get(event) || []
        listeners.forEach(listener => {
          try {
            listener(data)
          } catch (error) {
            console.error(`事件监听器执行失败 [${event}]:`, error)
          }
        })
      },
      on: (event, listener) => {
        if (!this.eventBus.listeners.has(event)) {
          this.eventBus.listeners.set(event, [])
        }
        this.eventBus.listeners.get(event).push(listener)
      },
      off: (event, listener) => {
        const listeners = this.eventBus.listeners.get(event) || []
        const index = listeners.indexOf(listener)
        if (index > -1) {
          listeners.splice(index, 1)
        }
      }
    }

    // 初始化
    this.init()
  }

  /**
   * 初始化管理器
   */
  async init() {
    try {
      // 加载现有数据状态
      await this.loadDataState()
      
      // 注册默认适配器
      this.registerDefaultAdapters()
      
      // 启动数据同步监控
      this.startSyncMonitoring()
      
      // 统一数据状态管理器初始化完成
    } catch (error) {
      console.error('统一数据状态管理器初始化失败:', error)
    }
  }

  /**
   * 加载数据状态
   */
  async loadDataState() {
    try {
      // 加载所有存储键的数据
      for (const [key, storageKey] of Object.entries(this.storageKeyMap)) {
        try {
          const data = wx.getStorageSync(storageKey)
          if (data) {
            this.dataState.set(key, {
              data,
              lastUpdate: Date.now(),
              source: 'storage'
            })
          }
        } catch (error) {
          console.warn(`加载数据失败 [${key}]:`, error)
        }
      }
    } catch (error) {
      console.error('加载数据状态失败:', error)
    }
  }

  /**
   * 注册数据适配器
   */
  registerAdapter(name, adapter) {
    this.adapters.set(name, adapter)
    // 数据适配器已注册
  }

  /**
   * 注册默认适配器
   */
  registerDefaultAdapters() {
    // WebSocket适配器
    this.registerAdapter('websocket', {
      read: async (key) => {
        if (key === 'expenseRecords') {
          // 从WebSocket管理器读取数据
          const wsData = wx.getStorageSync(this.storageKeyMap.wsExpenseRecords)
          if (wsData && wsData.data) {
            return this.decompressData(wsData)
          }
        }
        return null
      },
      write: async (key, data) => {
        if (key === 'expenseRecords') {
          // 写入到WebSocket存储
          const compressed = this.compressData(data)
          wx.setStorageSync(this.storageKeyMap.wsExpenseRecords, compressed)
          return true
        }
        return false
      }
    })

    // 简单数据管理器适配器
    this.registerAdapter('simpleDataManager', {
      read: async (key) => {
        const storageKey = this.storageKeyMap[key]
        if (storageKey) {
          const data = wx.getStorageSync(storageKey)
          return data
        }
        return null
      },
      write: async (key, data) => {
        const storageKey = this.storageKeyMap[key]
        if (storageKey) {
          wx.setStorageSync(storageKey, data)
          return true
        }
        return false
      }
    })
  }

  /**
   * 统一数据读取接口
   */
  async getData(key, options = {}) {
    try {
      const {
        forceRefresh = false,
        preferredAdapter = null,
        fallbackToCache = true
      } = options

      // 检查内存状态
      if (!forceRefresh && this.dataState.has(key)) {
        const state = this.dataState.get(key)
        // 检查数据是否过期（5分钟）
        if (Date.now() - state.lastUpdate < 5 * 60 * 1000) {
          return { success: true, data: state.data, source: 'memory' }
        }
      }

      // 尝试从适配器读取
      const adaptersToTry = preferredAdapter
        ? [preferredAdapter, ...Array.from(this.adapters.keys()).filter(a => a !== preferredAdapter)]
        : Array.from(this.adapters.keys())

      for (const adapterName of adaptersToTry) {
        try {
          const adapter = this.adapters.get(adapterName)
          const data = await adapter.read(key)

          if (data && this.isValidData(data)) {
            // 更新内存状态
            this.dataState.set(key, {
              data,
              lastUpdate: Date.now(),
              source: adapterName
            })

            return { success: true, data, source: adapterName }
          }
        } catch (error) {
          // 静默处理适配器错误
        }
      }

      // 如果所有适配器都失败，返回空数据
      return { success: true, data: null, source: 'empty' }

    } catch (error) {
      console.error(`统一数据读取失败 [${key}]:`, error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 统一数据写入接口
   */
  async setData(key, data, options = {}) {
    try {
      const { 
        syncToAll = true, 
        notifyObservers = true,
        source = 'unknown' 
      } = options

      // 更新内存状态
      this.dataState.set(key, {
        data,
        lastUpdate: Date.now(),
        source
      })

      // 同步到所有适配器
      if (syncToAll) {
        const writePromises = Array.from(this.adapters.entries()).map(async ([name, adapter]) => {
          try {
            await adapter.write(key, data)
          } catch (error) {
            // 静默处理写入错误
          }
        })

        await Promise.allSettled(writePromises)
      }

      // 通知观察者
      if (notifyObservers) {
        this.notifyObservers(key, data)
      }

      // 发送数据变更事件
      this.eventBus.emit('dataChanged', { key, data, source })

      return { success: true }

    } catch (error) {
      console.error(`统一数据写入失败 [${key}]:`, error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 添加记账记录（专用接口）
   */
  async addExpenseRecord(recordData, options = {}) {
    try {
      const { source = 'websocket' } = options

      // 获取现有记录
      const existingResult = await this.getData('expenseRecords')
      const existingRecords = existingResult.success ? (existingResult.data || []) : []

      // 添加新记录
      const newRecord = {
        ...recordData,
        _id: recordData._id || `unified_${Date.now()}`,
        createTime: recordData.createTime || new Date().toISOString(),
        source: source
      }

      const updatedRecords = [newRecord, ...existingRecords]

      // 保存更新后的记录
      await this.setData('expenseRecords', updatedRecords, { source })

      // 同时更新兼容性缓存
      await this.setData('expenseRecordsCache', updatedRecords, { source })

      // 触发财务数据重新计算
      this.eventBus.emit('expenseRecordAdded', newRecord)

      return { success: true, record: newRecord }

    } catch (error) {
      console.error('添加记账记录失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 注册数据观察者
   */
  subscribe(key, observer) {
    if (!this.observers.has(key)) {
      this.observers.set(key, [])
    }
    this.observers.get(key).push(observer)
    
    return () => {
      // 返回取消订阅函数
      const observers = this.observers.get(key) || []
      const index = observers.indexOf(observer)
      if (index > -1) {
        observers.splice(index, 1)
      }
    }
  }

  /**
   * 通知观察者
   */
  notifyObservers(key, data) {
    const observers = this.observers.get(key) || []
    observers.forEach(observer => {
      try {
        observer(data, key)
      } catch (error) {
        // 观察者通知失败
      }
    })
  }

  /**
   * 数据验证
   */
  isValidData(data) {
    if (!data) return false

    // 检查是否是损坏的字符数组
    if (Array.isArray(data) && data.length > 0) {
      const firstItem = data[0]
      // 如果第一个元素是字符串而不是对象，说明数据损坏
      if (typeof firstItem === 'string' && firstItem.length === 1) {
        return false
      }
    }

    return true
  }

  /**
   * 数据压缩（简单实现）
   */
  compressData(data) {
    try {
      const jsonStr = JSON.stringify(data)
      return {
        compressed: true,
        data: jsonStr,
        size: jsonStr.length,
        timestamp: Date.now()
      }
    } catch (error) {
      return { compressed: false, data }
    }
  }

  /**
   * 数据解压缩
   */
  decompressData(compressedData) {
    try {
      if (compressedData.compressed) {
        return JSON.parse(compressedData.data)
      }
      return compressedData.data
    } catch (error) {
      console.error('数据解压缩失败:', error)
      return null
    }
  }

  /**
   * 启动同步监控
   */
  startSyncMonitoring() {
    // 禁用自动数据一致性检查，减少性能开销
  }

  /**
   * 检查数据一致性
   */
  async checkDataConsistency() {
    // 简化数据一致性检查，只在必要时执行
  }

  /**
   * 清除指定键的缓存
   */
  clearCache(key) {
    if (this.dataState.has(key)) {
      this.dataState.delete(key)
    }
  }

  /**
   * 获取管理器状态
   */
  getStatus() {
    return {
      dataStateSize: this.dataState.size,
      observersCount: Array.from(this.observers.values()).reduce((sum, obs) => sum + obs.length, 0),
      adaptersCount: this.adapters.size,
      syncStatus: this.syncStatus,
      lastCheck: Date.now()
    }
  }

  /**
   * 清理资源
   */
  destroy() {
    this.observers.clear()
    this.adapters.clear()
    this.dataState.clear()
    this.eventBus.listeners.clear()
    // 统一数据状态管理器已销毁
  }
}

// 创建全局实例
const unifiedDataStateManager = new UnifiedDataStateManager()

export default unifiedDataStateManager
export { UnifiedDataStateManager }
