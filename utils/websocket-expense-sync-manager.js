/**
 * WebSocket记账数据同步管理器
 * 基于现有WebSocket基础设施，实现记账数据的实时同步
 * 本地存储控制在60%以下，支持数据压缩和智能清理
 */

import cloudRunWebSocketManager from './cloudrun-websocket-manager.js'
import unifiedDataStateManager from './unified-data-state-manager.js'

class WebSocketExpenseSyncManager {
  constructor() {
    // 存储配置 - 严格控制在6MB以下（60%）
    this.storageConfig = {
      maxTotalSize: 6 * 1024 * 1024, // 6MB总限制
      maxExpenseRecords: 1000,       // 最多存储1000条记录
      maxSyncQueue: 100,             // 最多100条待同步记录
      compressionThreshold: 1024,    // 超过1KB的数据进行压缩
      cleanupThreshold: 0.8          // 达到80%时触发清理
    }

    // 本地存储键名
    this.storageKeys = {
      expenseRecords: 'ws_expense_records',
      syncQueue: 'ws_sync_queue',
      syncStatus: 'ws_sync_status',
      storageInfo: 'ws_storage_info'
    }

    // 同步状态
    this.syncStatus = {
      isOnline: false,
      lastSyncTime: 0,
      pendingCount: 0,
      failedCount: 0
    }

    // 数据压缩器（简化版本，避免JSON解析错误）
    this.compressor = {
      compress: (data) => {
        try {
          return {
            compressed: false,
            data: data,
            timestamp: Date.now()
          }
        } catch (error) {
          console.error('数据压缩失败:', error)
          return { compressed: false, data: data }
        }
      },

      decompress: (compressedData) => {
        try {
          if (!compressedData || typeof compressedData !== 'object') {
            return compressedData
          }

          if (compressedData.compressed === false) {
            return compressedData.data
          }

          // 如果是旧的压缩格式，尝试解析
          if (typeof compressedData.data === 'string') {
            try {
              return JSON.parse(compressedData.data)
            } catch (parseError) {
              console.warn('旧格式数据解析失败，返回空数组')
              return []
            }
          }

          return compressedData.data || []
        } catch (error) {
          console.error('数据解压缩失败:', error)
          return []
        }
      }
    }

    // 初始化
    this.init()
  }

  /**
   * 初始化管理器
   */
  async init() {
    try {
      // 加载本地状态
      this.loadLocalState()
      
      // 设置WebSocket事件监听
      this.setupWebSocketListeners()
      
      // 启动定期同步
      this.startPeriodicSync()
      
      // 启动存储监控
      this.startStorageMonitoring()
      
      console.log('WebSocket记账同步管理器初始化完成')
    } catch (error) {
      console.error('WebSocket记账同步管理器初始化失败:', error)
    }
  }

  /**
   * 加载本地状态
   */
  loadLocalState() {
    try {
      const storedStatus = wx.getStorageSync(this.storageKeys.syncStatus)
      if (storedStatus) {
        this.syncStatus = { ...this.syncStatus, ...storedStatus }
      }
    } catch (error) {
      console.error('加载本地状态失败:', error)
    }
  }

  /**
   * 保存本地状态
   */
  saveLocalState() {
    try {
      wx.setStorageSync(this.storageKeys.syncStatus, this.syncStatus)
    } catch (error) {
      console.error('保存本地状态失败:', error)
    }
  }

  /**
   * 设置WebSocket事件监听
   */
  setupWebSocketListeners() {
    // 连接成功
    cloudRunWebSocketManager.on('connected', () => {
      this.syncStatus.isOnline = true
      this.saveLocalState()
      console.log('WebSocket连接成功，开始同步待处理数据')
      this.syncPendingData()
    })

    // 连接断开
    cloudRunWebSocketManager.on('disconnected', () => {
      this.syncStatus.isOnline = false
      this.saveLocalState()
      console.log('WebSocket连接断开，切换到离线模式')
    })

    // 接收同步确认
    cloudRunWebSocketManager.on('message', (message) => {
      if (message.type === 'expense_sync_confirm') {
        this.handleSyncConfirm(message.data)
      }
    })
  }

  /**
   * 添加记账记录（主要接口）
   */
  async addExpenseRecord(recordData) {
    try {
      // 1. 立即保存到本地存储
      const localRecord = {
        ...recordData,
        _id: recordData._id || `local_${Date.now()}`,
        localId: `local_${Date.now()}`,
        status: 'local_saved', // 本地已保存
        createTime: new Date().toISOString(),
        syncStatus: 'pending'  // 待同步
      }

      // 2. 检查存储空间
      await this.checkStorageSpace()

      // 3. 保存到本地记录
      await this.saveToLocalRecords(localRecord)

      // 4. 添加到同步队列
      await this.addToSyncQueue(localRecord)

      // 5. 同步到统一数据状态管理器
      await unifiedDataStateManager.addExpenseRecord(localRecord, { source: 'websocket' })

      // 6. 检查WebSocket连接状态并同步
      const wsStatus = cloudRunWebSocketManager.getStatus()

      // 7. 如果在线，立即尝试同步
      if (this.syncStatus.isOnline && wsStatus.isConnected) {
        this.syncSingleRecord(localRecord)
      } else {
        // 备用方案：直接调用云函数
        this.syncViaCloudFunction(localRecord)
      }

      // 8. 记账成功后立即清除财务数据缓存
      this.clearFinancialCache()

      return {
        success: true,
        localId: localRecord.localId,
        status: 'local_saved',
        message: '记录已保存到本地，正在同步到云端...'
      }

    } catch (error) {
      console.error('添加记账记录失败:', error)
      return {
        success: false,
        message: '保存失败: ' + error.message
      }
    }
  }

  /**
   * 检查存储空间
   */
  async checkStorageSpace() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const currentSize = storageInfo.currentSize * 1024 // 转换为字节
      const maxSize = this.storageConfig.maxTotalSize
      
      // 如果超过阈值，触发清理
      if (currentSize > maxSize * this.storageConfig.cleanupThreshold) {
        console.log('存储空间不足，开始清理...')
        await this.cleanupStorage()
      }

      // 更新存储信息
      wx.setStorageSync(this.storageKeys.storageInfo, {
        currentSize,
        maxSize,
        usagePercent: (currentSize / maxSize * 100).toFixed(2),
        lastCheck: Date.now()
      })

    } catch (error) {
      console.error('检查存储空间失败:', error)
    }
  }

  /**
   * 保存到本地记录
   */
  async saveToLocalRecords(record) {
    try {
      // 获取现有记录
      let records = wx.getStorageSync(this.storageKeys.expenseRecords) || []

      // 如果获取的是压缩数据，先解压
      if (records && typeof records === 'object' && records.compressed) {
        records = this.compressor.decompress(records) || []
      }

      // 确保records是数组
      if (!Array.isArray(records)) {
        records = []
      }

      // 添加新记录
      records.unshift(record)

      // 限制记录数量
      if (records.length > this.storageConfig.maxExpenseRecords) {
        records = records.slice(0, this.storageConfig.maxExpenseRecords)
      }

      // 压缩并保存
      const compressedRecords = this.compressor.compress(records)
      wx.setStorageSync(this.storageKeys.expenseRecords, compressedRecords)

    } catch (error) {
      console.error('保存到本地记录失败:', error)
      throw error
    }
  }

  /**
   * 添加到同步队列
   */
  async addToSyncQueue(record) {
    try {
      let syncQueue = wx.getStorageSync(this.storageKeys.syncQueue) || []
      
      // 检查是否已存在
      const existingIndex = syncQueue.findIndex(item => item.localId === record.localId)
      if (existingIndex >= 0) {
        syncQueue[existingIndex] = record
      } else {
        syncQueue.unshift(record)
      }

      // 限制队列大小
      if (syncQueue.length > this.storageConfig.maxSyncQueue) {
        syncQueue = syncQueue.slice(0, this.storageConfig.maxSyncQueue)
      }

      wx.setStorageSync(this.storageKeys.syncQueue, syncQueue)
      this.syncStatus.pendingCount = syncQueue.length
      this.saveLocalState()

    } catch (error) {
      console.error('添加到同步队列失败:', error)
      throw error
    }
  }

  /**
   * 同步单条记录
   */
  async syncSingleRecord(record) {
    try {
      if (!this.syncStatus.isOnline) {
        console.log('同步管理器显示离线状态，无法同步')
        return false
      }

      console.log('准备同步记录:', record.localId)
      console.log('记录数据:', JSON.stringify(record, null, 2))

      // 通过WebSocket发送同步请求
      const syncMessage = {
        type: 'expense_sync_request',
        data: {
          localId: record.localId,
          recordData: record,
          timestamp: Date.now()
        }
      }

      console.log('发送WebSocket同步消息:', JSON.stringify(syncMessage, null, 2))

      const messageId = cloudRunWebSocketManager.send(syncMessage)
      console.log('WebSocket消息已发送，messageId:', messageId)

      // 更新记录状态
      await this.updateRecordStatus(record.localId, 'syncing')
      console.log('记录状态已更新为syncing')

      return true
    } catch (error) {
      console.error('同步单条记录失败:', error)
      await this.updateRecordStatus(record.localId, 'sync_failed')
      return false
    }
  }

  /**
   * 更新记录状态
   */
  async updateRecordStatus(localId, status) {
    try {
      // 更新本地记录
      const compressedRecords = wx.getStorageSync(this.storageKeys.expenseRecords)
      if (compressedRecords) {
        let records = compressedRecords

        // 如果是压缩数据，先解压
        if (typeof compressedRecords === 'object' && compressedRecords.compressed) {
          records = this.compressor.decompress(compressedRecords)
        }

        // 确保records是数组
        if (Array.isArray(records)) {
          const recordIndex = records.findIndex(r => r.localId === localId)
          if (recordIndex >= 0) {
            records[recordIndex].syncStatus = status
            records[recordIndex].lastSyncAttempt = Date.now()

            const updatedCompressed = this.compressor.compress(records)
            wx.setStorageSync(this.storageKeys.expenseRecords, updatedCompressed)
          }
        }
      }

      // 更新同步队列
      const syncQueue = wx.getStorageSync(this.storageKeys.syncQueue) || []
      const queueIndex = syncQueue.findIndex(r => r.localId === localId)
      if (queueIndex >= 0) {
        if (status === 'synced') {
          // 同步成功，从队列中移除
          syncQueue.splice(queueIndex, 1)
        } else {
          // 更新状态
          syncQueue[queueIndex].syncStatus = status
          syncQueue[queueIndex].lastSyncAttempt = Date.now()
        }
        wx.setStorageSync(this.storageKeys.syncQueue, syncQueue)
        this.syncStatus.pendingCount = syncQueue.length
        this.saveLocalState()
      }

    } catch (error) {
      console.error('更新记录状态失败:', error)
    }
  }

  /**
   * 处理同步确认
   */
  handleSyncConfirm(data) {
    try {
      const { localId, success, cloudId, error } = data

      if (success) {
        // 同步成功
        this.updateRecordStatus(localId, 'synced')
        console.log(`记录同步成功: ${localId} -> ${cloudId}`)

        // 通知统一数据状态管理器同步成功
        unifiedDataStateManager.eventBus.emit('expenseRecordSynced', { localId, cloudId })
      } else {
        // 同步失败
        this.updateRecordStatus(localId, 'sync_failed')
        console.error(`记录同步失败: ${localId}`, error)
        this.syncStatus.failedCount++
        this.saveLocalState()

        // 通知统一数据状态管理器同步失败
        unifiedDataStateManager.eventBus.emit('expenseRecordSyncFailed', { localId, error })
      }
    } catch (error) {
      console.error('处理同步确认失败:', error)
    }
  }

  /**
   * 同步待处理数据
   */
  async syncPendingData() {
    try {
      const syncQueue = wx.getStorageSync(this.storageKeys.syncQueue) || []

      if (syncQueue.length === 0) {
        console.log('没有待同步的数据')
        return
      }

      console.log(`开始同步 ${syncQueue.length} 条待处理记录`)

      // 批量同步（每次最多10条）
      const batchSize = 10
      for (let i = 0; i < syncQueue.length; i += batchSize) {
        const batch = syncQueue.slice(i, i + batchSize)
        await this.syncBatch(batch)

        // 避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

    } catch (error) {
      console.error('同步待处理数据失败:', error)
    }
  }

  /**
   * 批量同步
   */
  async syncBatch(records) {
    try {
      const batchMessage = {
        type: 'expense_batch_sync_request',
        data: {
          records: records.map(r => ({
            localId: r.localId,
            recordData: r
          })),
          timestamp: Date.now()
        }
      }

      cloudRunWebSocketManager.send(batchMessage)

      // 更新所有记录状态为同步中
      for (const record of records) {
        await this.updateRecordStatus(record.localId, 'syncing')
      }

    } catch (error) {
      console.error('批量同步失败:', error)
    }
  }

  /**
   * 启动定期同步
   */
  startPeriodicSync() {
    // 每5分钟尝试同步一次
    setInterval(() => {
      if (this.syncStatus.isOnline && this.syncStatus.pendingCount > 0) {
        console.log('定期同步检查...')
        this.syncPendingData()
      }
    }, 5 * 60 * 1000)
  }

  /**
   * 启动存储监控
   */
  startStorageMonitoring() {
    // 每10分钟检查一次存储空间
    setInterval(() => {
      this.checkStorageSpace()
    }, 10 * 60 * 1000)
  }

  /**
   * 清理存储空间
   */
  async cleanupStorage() {
    try {
      console.log('开始清理存储空间...')

      // 1. 清理已同步的旧记录
      const compressedRecords = wx.getStorageSync(this.storageKeys.expenseRecords)
      if (compressedRecords) {
        let records = compressedRecords

        // 如果是压缩数据，先解压
        if (typeof compressedRecords === 'object' && compressedRecords.compressed) {
          records = this.compressor.decompress(compressedRecords)
        }

        // 确保records是数组
        if (Array.isArray(records)) {
          // 保留最近500条记录，删除已同步的旧记录
          const recentRecords = records
            .filter(r => r.syncStatus !== 'synced' || Date.now() - new Date(r.createTime).getTime() < 7 * 24 * 60 * 60 * 1000) // 保留7天内的记录
            .slice(0, 500)

          const cleanedCompressed = this.compressor.compress(recentRecords)
          wx.setStorageSync(this.storageKeys.expenseRecords, cleanedCompressed)

          console.log(`清理记录: ${records.length} -> ${recentRecords.length}`)
        }
      }

      // 2. 清理失败的同步队列项
      const syncQueue = wx.getStorageSync(this.storageKeys.syncQueue) || []
      const validQueue = syncQueue.filter(item => {
        // 保留最近24小时内的失败记录
        return Date.now() - (item.lastSyncAttempt || item.createTime) < 24 * 60 * 60 * 1000
      })

      if (validQueue.length !== syncQueue.length) {
        wx.setStorageSync(this.storageKeys.syncQueue, validQueue)
        this.syncStatus.pendingCount = validQueue.length
        this.saveLocalState()
        console.log(`清理同步队列: ${syncQueue.length} -> ${validQueue.length}`)
      }

      console.log('存储空间清理完成')

    } catch (error) {
      console.error('清理存储空间失败:', error)
    }
  }

  /**
   * 获取本地记录
   */
  getLocalRecords(options = {}) {
    try {
      const { limit = 50, offset = 0, status = null } = options

      const compressedRecords = wx.getStorageSync(this.storageKeys.expenseRecords)
      if (!compressedRecords) {
        return { success: true, data: [], total: 0 }
      }

      let records = compressedRecords

      // 如果是压缩数据，先解压
      if (typeof compressedRecords === 'object' && compressedRecords.compressed) {
        records = this.compressor.decompress(compressedRecords)
      }

      // 确保records是数组
      if (!Array.isArray(records)) {
        return { success: true, data: [], total: 0 }
      }

      // 过滤状态
      let filteredRecords = records
      if (status) {
        filteredRecords = records.filter(r => r.syncStatus === status)
      }

      // 分页
      const paginatedRecords = filteredRecords.slice(offset, offset + limit)

      return {
        success: true,
        data: paginatedRecords,
        total: filteredRecords.length,
        hasMore: offset + limit < filteredRecords.length
      }

    } catch (error) {
      console.error('获取本地记录失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    const storageInfo = wx.getStorageInfoSync()

    return {
      ...this.syncStatus,
      storage: {
        currentSize: storageInfo.currentSize,
        limitSize: storageInfo.limitSize,
        usagePercent: ((storageInfo.currentSize / storageInfo.limitSize) * 100).toFixed(2)
      }
    }
  }

  /**
   * 强制同步所有数据
   */
  async forceSyncAll() {
    if (!this.syncStatus.isOnline) {
      throw new Error('WebSocket未连接，无法同步')
    }

    console.log('开始强制同步所有数据...')
    await this.syncPendingData()
  }

  /**
   * 备用同步方案：直接调用云函数
   */
  async syncViaCloudFunction(record) {
    try {
      // 更新记录状态
      await this.updateRecordStatus(record.localId, 'syncing')

      // 直接调用云函数
      const result = await wx.cloud.callFunction({
        name: 'expense',
        data: {
          action: 'addExpenseRecord',
          data: record
        }
      })

      if (result.result && result.result.success) {
        const cloudId = result.result.data.id

        // 更新记录状态为已同步
        await this.updateRecordStatus(record.localId, 'synced')

        // 通知统一数据状态管理器同步成功
        unifiedDataStateManager.eventBus.emit('expenseRecordSynced', {
          localId: record.localId,
          cloudId
        })

        return true
      } else {
        throw new Error(result.result?.message || '云函数调用失败')
      }

    } catch (error) {
      await this.updateRecordStatus(record.localId, 'sync_failed')

      // 通知统一数据状态管理器同步失败
      unifiedDataStateManager.eventBus.emit('expenseRecordSyncFailed', {
        localId: record.localId,
        error: error.message
      })

      return false
    }
  }

  /**
   * 清除财务数据缓存
   */
  clearFinancialCache() {
    try {
      // 清除统一数据状态管理器中的财务数据缓存
      unifiedDataStateManager.clearCache('financialOverview')

      // 清除简单数据管理器中的财务数据缓存
      wx.removeStorageSync('financial_overview')

      // 触发首页数据刷新事件
      unifiedDataStateManager.eventBus.emit('financialDataChanged')

    } catch (error) {
      // 静默处理清除缓存错误
    }
  }

  /**
   * 清空所有本地数据（危险操作）
   */
  clearAllLocalData() {
    try {
      wx.removeStorageSync(this.storageKeys.expenseRecords)
      wx.removeStorageSync(this.storageKeys.syncQueue)
      wx.removeStorageSync(this.storageKeys.storageInfo)

      this.syncStatus.pendingCount = 0
      this.syncStatus.failedCount = 0
      this.saveLocalState()

      console.log('所有本地数据已清空')
      return true
    } catch (error) {
      console.error('清空本地数据失败:', error)
      return false
    }
  }
}

// 创建全局实例
const websocketExpenseSyncManager = new WebSocketExpenseSyncManager()

export default websocketExpenseSyncManager
export { WebSocketExpenseSyncManager }
