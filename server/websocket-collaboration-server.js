/**
 * WebSocket协作服务器
 * 基于官方ws库实现，用于小程序实时协作功能
 */

import { WebSocketServer } from 'ws'
import { createServer } from 'http'
import { URL } from 'url'

class CollaborationServer {
  constructor(options = {}) {
    this.config = {
      port: options.port || 8080,
      host: options.host || 'localhost',
      perMessageDeflate: true,
      maxPayload: 1024 * 1024, // 1MB
      ...options
    }

    // HTTP服务器
    this.httpServer = null
    
    // WebSocket服务器实例
    this.wss = null
    
    // 连接管理
    this.connections = new Map() // 存储所有连接
    this.rooms = new Map() // 房间管理
    this.userSessions = new Map() // 用户会话管理
    
    // 统计信息
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      totalMessages: 0,
      roomCount: 0,
      startTime: Date.now()
    }

    // 心跳配置
    this.heartbeatInterval = 30000 // 30秒
    this.heartbeatTimer = null
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      // 创建HTTP服务器
      this.httpServer = createServer()
      
      // 创建WebSocket服务器
      this.wss = new WebSocketServer({
        server: this.httpServer,
        perMessageDeflate: this.config.perMessageDeflate,
        maxPayload: this.config.maxPayload
      })

      // 设置事件监听器
      this.setupEventListeners()
      
      // 启动心跳检测
      this.startHeartbeat()

      // 启动HTTP服务器
      await new Promise((resolve, reject) => {
        this.httpServer.listen(this.config.port, this.config.host, (error) => {
          if (error) {
            reject(error)
          } else {
            console.log(`WebSocket协作服务器已启动: ws://${this.config.host}:${this.config.port}`)
            resolve()
          }
        })
      })

      return true
    } catch (error) {
      console.error('服务器启动失败:', error)
      return false
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // WebSocket连接事件
    this.wss.on('connection', (ws, request) => {
      this.handleConnection(ws, request)
    })

    // HTTP升级事件（用于认证）
    this.httpServer.on('upgrade', (request, socket, head) => {
      this.handleUpgrade(request, socket, head)
    })

    // 服务器错误处理
    this.wss.on('error', (error) => {
      console.error('WebSocket服务器错误:', error)
    })
  }

  /**
   * 处理HTTP升级请求（认证）
   */
  handleUpgrade(request, socket, head) {
    // 简单的认证逻辑（实际项目中应该更严格）
    const url = new URL(request.url, `http://${request.headers.host}`)
    const token = url.searchParams.get('token')
    const userId = url.searchParams.get('userId')

    if (!token || !userId) {
      socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n')
      socket.destroy()
      return
    }

    // 验证token（这里简化处理）
    if (!this.validateToken(token, userId)) {
      socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n')
      socket.destroy()
      return
    }

    // 认证通过，处理WebSocket升级
    this.wss.handleUpgrade(request, socket, head, (ws) => {
      this.wss.emit('connection', ws, request)
    })
  }

  /**
   * 验证token（简化实现）
   */
  validateToken(token, userId) {
    // 实际项目中应该验证JWT或其他认证方式
    return token && userId && token.length > 10
  }

  /**
   * 处理新连接
   */
  handleConnection(ws, request) {
    const url = new URL(request.url, `http://${request.headers.host}`)
    const userId = url.searchParams.get('userId')
    const roomId = url.searchParams.get('roomId')
    
    // 生成连接ID
    const connectionId = this.generateConnectionId()
    
    // 连接信息
    const connectionInfo = {
      id: connectionId,
      userId,
      roomId,
      ws,
      isAlive: true,
      joinTime: Date.now(),
      lastActivity: Date.now()
    }

    // 存储连接
    this.connections.set(connectionId, connectionInfo)
    this.stats.totalConnections++
    this.stats.activeConnections++

    console.log(`新连接: ${userId} 加入房间 ${roomId} (${connectionId})`)

    // 加入房间
    if (roomId) {
      this.joinRoom(connectionId, roomId)
    }

    // 设置连接事件监听器
    this.setupConnectionListeners(connectionId, ws)

    // 发送连接成功消息
    this.sendToConnection(connectionId, {
      type: 'connected',
      data: {
        connectionId,
        serverTime: Date.now()
      }
    })
  }

  /**
   * 设置连接事件监听器
   */
  setupConnectionListeners(connectionId, ws) {
    // 消息处理
    ws.on('message', (data) => {
      this.handleMessage(connectionId, data)
    })

    // 连接关闭
    ws.on('close', (code, reason) => {
      this.handleDisconnection(connectionId, code, reason)
    })

    // 连接错误
    ws.on('error', (error) => {
      console.error(`连接错误 ${connectionId}:`, error)
      this.handleDisconnection(connectionId, 1006, 'error')
    })

    // 心跳响应
    ws.on('pong', () => {
      const connection = this.connections.get(connectionId)
      if (connection) {
        connection.isAlive = true
        connection.lastActivity = Date.now()
      }
    })
  }

  /**
   * 处理消息
   */
  handleMessage(connectionId, data) {
    try {
      const connection = this.connections.get(connectionId)
      if (!connection) return

      connection.lastActivity = Date.now()
      this.stats.totalMessages++

      const message = JSON.parse(data.toString())
      
      // 根据消息类型处理
      switch (message.type) {
        case 'heartbeat':
          this.handleHeartbeat(connectionId, message)
          break
        case 'join_room':
          this.handleJoinRoom(connectionId, message)
          break
        case 'leave_room':
          this.handleLeaveRoom(connectionId, message)
          break
        case 'travel_update':
          this.handleTravelUpdate(connectionId, message)
          break
        case 'expense_update':
          this.handleExpenseUpdate(connectionId, message)
          break
        case 'collaboration_message':
          this.handleCollaborationMessage(connectionId, message)
          break
        default:
          console.warn(`未知消息类型: ${message.type}`)
      }

    } catch (error) {
      console.error(`消息处理错误 ${connectionId}:`, error)
    }
  }

  /**
   * 处理心跳
   */
  handleHeartbeat(connectionId, message) {
    this.sendToConnection(connectionId, {
      type: 'heartbeat',
      data: { pong: Date.now() }
    })
  }

  /**
   * 处理加入房间
   */
  handleJoinRoom(connectionId, message) {
    const { roomId } = message.data
    this.joinRoom(connectionId, roomId)
  }

  /**
   * 处理离开房间
   */
  handleLeaveRoom(connectionId, message) {
    const { roomId } = message.data
    this.leaveRoom(connectionId, roomId)
  }

  /**
   * 处理旅行计划更新
   */
  handleTravelUpdate(connectionId, message) {
    const connection = this.connections.get(connectionId)
    if (!connection || !connection.roomId) return

    // 广播给房间内其他用户
    this.broadcastToRoom(connection.roomId, {
      type: 'travel_update',
      data: {
        ...message.data,
        userId: connection.userId,
        timestamp: Date.now()
      }
    }, connectionId)
  }

  /**
   * 处理支出更新
   */
  handleExpenseUpdate(connectionId, message) {
    const connection = this.connections.get(connectionId)
    if (!connection || !connection.roomId) return

    // 广播给房间内其他用户
    this.broadcastToRoom(connection.roomId, {
      type: 'expense_update',
      data: {
        ...message.data,
        userId: connection.userId,
        timestamp: Date.now()
      }
    }, connectionId)
  }

  /**
   * 处理协作消息
   */
  handleCollaborationMessage(connectionId, message) {
    const connection = this.connections.get(connectionId)
    if (!connection || !connection.roomId) return

    // 广播给房间内其他用户
    this.broadcastToRoom(connection.roomId, {
      type: 'collaboration_message',
      data: {
        ...message.data,
        userId: connection.userId,
        timestamp: Date.now()
      }
    }, connectionId)
  }

  /**
   * 加入房间
   */
  joinRoom(connectionId, roomId) {
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set())
      this.stats.roomCount++
    }

    const room = this.rooms.get(roomId)
    room.add(connectionId)

    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.roomId = roomId
    }

    console.log(`连接 ${connectionId} 加入房间 ${roomId}`)

    // 通知房间内其他用户
    this.broadcastToRoom(roomId, {
      type: 'user_joined',
      data: {
        userId: connection?.userId,
        roomId,
        timestamp: Date.now()
      }
    }, connectionId)
  }

  /**
   * 离开房间
   */
  leaveRoom(connectionId, roomId) {
    const room = this.rooms.get(roomId)
    if (room) {
      room.delete(connectionId)
      
      if (room.size === 0) {
        this.rooms.delete(roomId)
        this.stats.roomCount--
      }
    }

    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.roomId = null
    }

    console.log(`连接 ${connectionId} 离开房间 ${roomId}`)

    // 通知房间内其他用户
    this.broadcastToRoom(roomId, {
      type: 'user_left',
      data: {
        userId: connection?.userId,
        roomId,
        timestamp: Date.now()
      }
    }, connectionId)
  }

  /**
   * 处理连接断开
   */
  handleDisconnection(connectionId, code, reason) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    console.log(`连接断开: ${connection.userId} (${connectionId}) - ${code} ${reason}`)

    // 离开房间
    if (connection.roomId) {
      this.leaveRoom(connectionId, connection.roomId)
    }

    // 移除连接
    this.connections.delete(connectionId)
    this.stats.activeConnections--
  }

  /**
   * 发送消息给指定连接
   */
  sendToConnection(connectionId, message) {
    const connection = this.connections.get(connectionId)
    if (!connection || connection.ws.readyState !== 1) return false

    try {
      connection.ws.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error(`发送消息失败 ${connectionId}:`, error)
      return false
    }
  }

  /**
   * 广播消息给房间内所有用户
   */
  broadcastToRoom(roomId, message, excludeConnectionId = null) {
    const room = this.rooms.get(roomId)
    if (!room) return

    let sentCount = 0
    room.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        if (this.sendToConnection(connectionId, message)) {
          sentCount++
        }
      }
    })

    return sentCount
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.connections.forEach((connection, connectionId) => {
        if (!connection.isAlive) {
          console.log(`心跳超时，断开连接: ${connectionId}`)
          connection.ws.terminate()
          this.handleDisconnection(connectionId, 1006, 'heartbeat timeout')
          return
        }

        connection.isAlive = false
        connection.ws.ping()
      })
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 获取服务器状态
   */
  getStatus() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      rooms: Array.from(this.rooms.keys()),
      connections: Array.from(this.connections.keys())
    }
  }

  /**
   * 停止服务器
   */
  async stop() {
    console.log('正在停止WebSocket服务器...')
    
    this.stopHeartbeat()
    
    // 关闭所有连接
    this.connections.forEach((connection) => {
      connection.ws.close(1001, '服务器关闭')
    })

    // 关闭WebSocket服务器
    if (this.wss) {
      this.wss.close()
    }

    // 关闭HTTP服务器
    if (this.httpServer) {
      await new Promise((resolve) => {
        this.httpServer.close(resolve)
      })
    }

    console.log('WebSocket服务器已停止')
  }

  /**
   * 生成连接ID
   */
  generateConnectionId() {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

export default CollaborationServer

// 如果直接运行此文件，启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new CollaborationServer({
    port: process.env.PORT || 8080,
    host: process.env.HOST || '0.0.0.0'
  })

  server.start().then(() => {
    console.log('协作服务器启动成功')
  }).catch((error) => {
    console.error('协作服务器启动失败:', error)
    process.exit(1)
  })

  // 优雅关闭
  process.on('SIGINT', async () => {
    await server.stop()
    process.exit(0)
  })

  process.on('SIGTERM', async () => {
    await server.stop()
    process.exit(0)
  })
}
