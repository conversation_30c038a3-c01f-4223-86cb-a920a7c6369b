{"name": "miniprogram-collaboration-server", "version": "1.0.0", "description": "WebSocket协作服务器，用于小程序实时协作功能", "type": "module", "main": "websocket-collaboration-server.js", "scripts": {"start": "node websocket-collaboration-server.js", "dev": "node --watch websocket-collaboration-server.js", "test": "node test/server.test.js"}, "keywords": ["websocket", "collaboration", "miniprogram", "realtime", "server"], "author": "Your Name", "license": "MIT", "dependencies": {"ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/miniprogram-collaboration.git"}, "bugs": {"url": "https://github.com/your-username/miniprogram-collaboration/issues"}, "homepage": "https://github.com/your-username/miniprogram-collaboration#readme"}