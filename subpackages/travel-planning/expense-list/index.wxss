/* subpackages/travel-planning/expense-list/index.wxss */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  padding-bottom: 120rpx;
}

/* 页面使用微信原生导航栏，无需自定义导航栏样式 */

/* 统计信息 - 项目标准设计 */
.stats-section {
  padding: 32rpx 32rpx 24rpx;
  position: relative;
  z-index: 10;
}

.stats-card {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  overflow: hidden;
  box-shadow:
    0 16rpx 64rpx rgba(255, 107, 107, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.3), transparent);
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 24rpx;
  gap: 12rpx;
  position: relative;
}

/* 协作状态显示 */
.stats-item.collaboration-status {
  flex: 2;
  padding: 24rpx;
}

.collaboration-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  width: 100%;
}

.online-users {
  display: flex;
  align-items: center;
  gap: -8rpx;
}

.online-user {
  position: relative;
  z-index: 1;
}

.user-avatar {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-placeholder {
  color: white;
  font-size: 18rpx;
  font-weight: bold;
}

.online-dot {
  position: absolute;
  bottom: -2rpx;
  right: -2rpx;
  width: 12rpx;
  height: 12rpx;
  background: #52c41a;
  border: 2rpx solid white;
  border-radius: 50%;
  animation: onlinePulse 2s infinite;
}

@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.more-users {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: 600;
  color: #666;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
}

.connection-status.connected {
  background: rgba(82, 196, 26, 0.2);
}

.connection-status.connecting {
  background: rgba(250, 173, 20, 0.2);
}

.connection-status.error {
  background: rgba(255, 77, 79, 0.2);
}

.connection-status.disconnected {
  background: rgba(140, 140, 140, 0.2);
}

.connection-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #8c8c8c;
}

.connection-status.connected .connection-dot {
  background: #52c41a;
  animation: connectionPulse 2s infinite;
}

.connection-status.connecting .connection-dot {
  background: #faad14;
  animation: connectionBlink 1s infinite;
}

.connection-status.error .connection-dot {
  background: #ff4d4f;
}

.connection-text {
  font-size: 20rpx;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

.connection-status.connected .connection-text {
  color: rgba(82, 196, 26, 0.9);
}

.connection-status.connecting .connection-text {
  color: rgba(250, 173, 20, 0.9);
}

.connection-status.error .connection-text {
  color: rgba(255, 77, 79, 0.9);
}

@keyframes connectionPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes connectionBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.stats-item:first-child::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background: linear-gradient(180deg, transparent, rgba(255, 107, 107, 0.2), transparent);
}

.stats-label {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.stats-value {
  font-size: 44rpx;
  font-weight: 700;
  color: #FF6B6B;
  letter-spacing: -0.3rpx;
}

/* 内容区域 - 精致化设计 */
.content-section {
  padding: 0 32rpx;
}

/* 加载和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64rpx 32rpx;
}

.empty-icon {
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

.empty-action {
  margin-top: 24rpx;
}

.add-first-btn {
  padding: 24rpx 48rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  border-radius: 32rpx !important;
  box-shadow:
    0 8rpx 32rpx rgba(78, 205, 196, 0.3),
    0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
}

/* 费用记录列表 - 现代卡片设计 */
.expense-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 8rpx;
}

.expense-item-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
}

/* 正在操作状态覆盖层 */
.operating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(4rpx);
}

.operating-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 107, 107, 0.2);
  border-top: 3rpx solid #FF6B6B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.operating-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.expense-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20rpx);
  box-shadow:
    0 8rpx 32rpx rgba(255, 107, 107, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
}

/* 正在操作状态的支出项 */
.expense-item.operating {
  opacity: 0.6;
  transform: scale(0.98);
  pointer-events: none;
}

.expense-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.2), transparent);
}

.expense-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 1);
  border-color: rgba(255, 107, 107, 0.3);
  box-shadow:
    0 12rpx 48rpx rgba(255, 107, 107, 0.12),
    0 6rpx 24rpx rgba(0, 0, 0, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 1);
}

.expense-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.expense-desc {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
  letter-spacing: 0.2rpx;
}

.expense-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.expense-category, .expense-date, .expense-location {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
  letter-spacing: 0.1rpx;
}

.expense-category {
  padding: 4rpx 12rpx;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 12rpx;
  color: #FF6B6B;
  font-weight: 600;
}

.expense-location::before {
  content: "📍 ";
  margin-right: 2rpx;
}

.expense-amount {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 4rpx;
}

.expense-amount .amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF6B6B;
  letter-spacing: -0.3rpx;
}

/* 删除按钮 - 现代设计 */
.expense-actions {
  display: flex;
  align-items: center;
}

.delete-btn {
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8rpx 32rpx rgba(255, 71, 87, 0.3),
    0 4rpx 16rpx rgba(255, 71, 87, 0.2),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  position: relative;
}

/* 禁用状态的删除按钮 */
.delete-btn.disabled {
  background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
  box-shadow:
    0 4rpx 16rpx rgba(204, 204, 204, 0.2),
    0 2rpx 8rpx rgba(204, 204, 204, 0.1);
  pointer-events: none;
  opacity: 0.6;
}

.delete-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
}

.delete-btn:active {
  transform: scale(0.88);
  background: linear-gradient(135deg, #ff3742 0%, #ff2533 100%);
  border-color: rgba(255, 255, 255, 1);
  box-shadow:
    0 4rpx 16rpx rgba(255, 71, 87, 0.4),
    0 2rpx 8rpx rgba(255, 71, 87, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

/* 悬浮按钮已移除 */

/* Vant组件样式覆盖 */
.van-loading {
  color: #ffffff !important;
}

.van-empty__description {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 28rpx !important;
}

.van-button--primary {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
}
