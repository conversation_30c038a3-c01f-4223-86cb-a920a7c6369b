// subpackages/travel-planning/expense-list/index.js
// {{ AURA-X: Modify - 替换不存在的data-manager为simpleDataManager. Approval: 寸止(ID:1738056000). }}
const simpleDataManager = require('../../../utils/simple-data-manager.js').default
const unifiedPerformanceManager = require('../../../utils/unified-performance-manager.js').default
const cloudRunWebSocketManager = require('../../../utils/cloudrun-websocket-manager.js').default

Page({
  data: {
    planId: '',
    planTitle: '',
    expenseRecords: [],
    loading: false,
    totalAmount: 0,
    recordCount: 0,
    isEmpty: false,
    // WebSocket协作状态
    collaboration: {
      isConnected: false,
      roomId: null,
      onlineUsers: [],
      connectionStatus: 'disconnected', // disconnected, connecting, connected, error
      operatingUsers: [] // 正在操作的用户列表
    }
  },

  async onLoad(options) {
    if (options.planId) {
      this.setData({
        planId: options.planId,
        planTitle: options.planTitle || '旅行费用记录'
      })

      // 加载费用记录
      await this.loadExpenseRecords()

      // 初始化WebSocket实时协作
      await this.initializeWebSocketCollaboration()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  /**
   * 加载费用记录
   */
  async loadExpenseRecords() {
    try {
      this.setData({ loading: true })

      // {{ AURA-X: Modify - 使用本地缓存替代dataManager调用. Approval: 寸止(ID:1738056000). }}
      // 从本地缓存获取费用记录数据
      const allExpenses = simpleDataManager.getFromLocalStorage('expense_records_cache') || []

      // 筛选与当前旅行计划相关的费用记录
      const planExpenses = allExpenses
        .filter(expense => expense.travel_plan_id === this.data.planId || expense.planId === this.data.planId)
        .sort((a, b) => new Date(b.createTime || b.date) - new Date(a.createTime || a.date))
        .slice(0, 100) // 加载更多记录

      const result = {
        success: true,
        data: planExpenses
      }

      if (result.success && result.data) {
        // 格式化数据
        const formattedRecords = result.data.map(record => ({
          id: record._id,
          amount: record.amount,
          category: record.category?.main || '其他',
          description: record.description || record.category?.main || '支出',
          date: this.formatDate(record.createTime),
          fullDate: this.formatFullDate(record.createTime),
          location: record.location?.name || '',
          createTime: record.createTime
        }))

        // 计算总金额
        const totalAmount = formattedRecords.reduce((sum, record) => sum + record.amount, 0)

        this.setData({
          expenseRecords: formattedRecords,
          totalAmount: totalAmount,
          recordCount: formattedRecords.length,
          isEmpty: formattedRecords.length === 0
        })
      } else {
        this.setData({
          expenseRecords: [],
          totalAmount: 0,
          recordCount: 0,
          isEmpty: true
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({
        expenseRecords: [],
        isEmpty: true
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化日期显示 (MM-DD)
   */
  formatDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${month}-${day}`
    } catch (error) {
      return '今天'
    }
  },

  /**
   * 格式化完整日期显示 (YYYY-MM-DD HH:mm)
   */
  formatFullDate(dateInput) {
    try {
      let date
      if (dateInput instanceof Date) {
        date = dateInput
      } else if (typeof dateInput === 'string') {
        date = new Date(dateInput)
      } else if (dateInput && dateInput.$date) {
        date = new Date(dateInput.$date)
      } else {
        date = new Date()
      }

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      return '未知时间'
    }
  },

  /**
   * 查看费用详情
   */
  viewExpenseDetail(event) {
    const { record } = event.currentTarget.dataset
    // 这里可以跳转到费用详情页面或显示详情弹窗
    wx.showModal({
      title: '费用详情',
      content: `描述：${record.description}\n分类：${record.category}\n金额：¥${record.amount}\n时间：${record.fullDate}${record.location ? '\n地点：' + record.location : ''}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * {{ AURA-X: Modify - 使用统一删除处理器，提升安卓兼容性. Approval: 寸止(ID:1738056000). }}
   * 删除费用记录（增强WebSocket实时协作）
   */
  async deleteExpenseRecord(event) {
    const { record } = event.currentTarget.dataset
    if (!record || !record.id) {
      wx.showToast({
        title: '记录信息错误',
        icon: 'none'
      })
      return
    }

    try {
      // 发送正在删除的协作消息
      const userInfo = wx.getStorageSync('userInfo')
      this.sendCollaborationMessage('expense_operation', {
        operation: 'deleting',
        userId: userInfo.openid,
        nickname: userInfo.nickName || '用户',
        expenseId: record.id
      })

      // 使用统一删除处理器
      const unifiedDeleteHandler = require('../../../utils/unified-delete-handler.js').default

      const result = await unifiedDeleteHandler.deleteExpenseRecord(record.id, {
        confirmContent: `确定要删除这条费用记录吗？\n${record.description} ¥${record.amount}`,
        onSuccess: async () => {
          // 重新加载费用记录
          await this.loadExpenseRecords()

          // 发送删除成功的WebSocket更新
          this.sendExpenseUpdate('delete', {
            id: record.id,
            description: record.description,
            amount: record.amount,
            operatorName: userInfo.nickName || '用户'
          })
        },
        onError: (error) => {
          console.error('删除费用记录失败:', error)
        }
      })

      // 如果用户取消删除，不需要额外处理
      if (result.cancelled) {
        return
      }

    } catch (error) {
      console.error('删除费用记录异常:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 添加新费用
   */
  addExpense() {
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${this.data.planId}`
    })
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadExpenseRecords().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化WebSocket实时协作
   */
  async initializeWebSocketCollaboration() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.openid) {
        console.log('用户未登录，跳过WebSocket初始化')
        return
      }

      // 设置WebSocket事件监听器
      this.setupWebSocketListeners()

      // 尝试连接到云托管WebSocket服务
      const success = await cloudRunWebSocketManager.init('cloud1-8gazcu0g1d5b8a5a', 'websocket-service', {
        path: '/ws',
        timeout: 10000
      })

      if (success) {
        console.log('支出记录页WebSocket协作服务初始化成功')
        this.setData({
          'collaboration.connectionStatus': 'connected',
          'collaboration.isConnected': true
        })

        // 加入当前计划的支出协作房间
        await this.joinExpenseCollaborationRoom()
      } else {
        console.warn('支出记录页WebSocket协作服务初始化失败')
        this.setData({
          'collaboration.connectionStatus': 'error'
        })
      }

    } catch (error) {
      console.error('支出记录页WebSocket协作初始化错误:', error)
      this.setData({
        'collaboration.connectionStatus': 'error'
      })
    }
  },

  /**
   * 设置WebSocket事件监听器
   */
  setupWebSocketListeners() {
    // 连接成功
    cloudRunWebSocketManager.on('connected', () => {
      console.log('支出记录页WebSocket连接成功')
      this.setData({
        'collaboration.isConnected': true,
        'collaboration.connectionStatus': 'connected'
      })
    })

    // 连接断开
    cloudRunWebSocketManager.on('disconnected', () => {
      console.log('支出记录页WebSocket连接断开')
      this.setData({
        'collaboration.isConnected': false,
        'collaboration.connectionStatus': 'disconnected',
        'collaboration.onlineUsers': [],
        'collaboration.operatingUsers': []
      })
    })

    // 连接错误
    cloudRunWebSocketManager.on('error', (error) => {
      console.error('支出记录页WebSocket连接错误:', error)
      this.setData({
        'collaboration.connectionStatus': 'error'
      })
    })

    // 用户加入房间
    cloudRunWebSocketManager.on('user_joined', (data) => {
      console.log('用户加入支出协作房间:', data)
      this.handleUserJoined(data)
    })

    // 用户离开房间
    cloudRunWebSocketManager.on('user_left', (data) => {
      console.log('用户离开支出协作房间:', data)
      this.handleUserLeft(data)
    })

    // 支出更新（实时同步）
    cloudRunWebSocketManager.on('expense_update', (data) => {
      console.log('收到支出实时更新:', data)
      this.handleExpenseUpdate(data)
    })

    // 协作消息
    cloudRunWebSocketManager.on('collaboration_message', (data) => {
      console.log('收到协作消息:', data)
      this.handleCollaborationMessage(data)
    })
  },

  /**
   * 加入支出协作房间
   */
  async joinExpenseCollaborationRoom() {
    if (!this.data.collaboration.isConnected || !this.data.planId) {
      return false
    }

    try {
      const userInfo = wx.getStorageSync('userInfo')
      const roomId = `expense_${this.data.planId}`

      // 发送加入房间消息
      cloudRunWebSocketManager.send({
        planId: this.data.planId,
        roomId,
        userInfo: {
          userId: userInfo.openid,
          nickname: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl
        }
      }, 'join_room')

      this.setData({
        'collaboration.roomId': roomId
      })

      console.log(`已加入支出协作房间: ${roomId}`)
      return true

    } catch (error) {
      console.error('加入支出协作房间失败:', error)
      return false
    }
  },

  /**
   * 离开支出协作房间
   */
  async leaveExpenseCollaborationRoom() {
    if (!this.data.collaboration.isConnected || !this.data.collaboration.roomId) {
      return
    }

    try {
      // 发送离开房间消息
      cloudRunWebSocketManager.send({
        roomId: this.data.collaboration.roomId
      }, 'leave_room')

      this.setData({
        'collaboration.roomId': null,
        'collaboration.onlineUsers': [],
        'collaboration.operatingUsers': []
      })

      console.log('已离开支出协作房间')

    } catch (error) {
      console.error('离开支出协作房间失败:', error)
    }
  },

  /**
   * 处理用户加入房间
   */
  handleUserJoined(data) {
    const { userId, roomId } = data
    if (roomId !== this.data.collaboration.roomId) return

    // 更新在线用户列表
    const onlineUsers = [...this.data.collaboration.onlineUsers]
    if (!onlineUsers.find(user => user.userId === userId)) {
      onlineUsers.push({
        userId,
        nickname: data.nickname || '协作者',
        avatarUrl: data.avatarUrl,
        joinTime: Date.now()
      })

      this.setData({
        'collaboration.onlineUsers': onlineUsers
      })

      // 显示用户加入提示
      wx.showToast({
        title: `${data.nickname || '协作者'}加入了协作`,
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 处理用户离开房间
   */
  handleUserLeft(data) {
    const { userId, roomId } = data
    if (roomId !== this.data.collaboration.roomId) return

    // 更新在线用户列表
    const onlineUsers = this.data.collaboration.onlineUsers.filter(user => user.userId !== userId)
    const operatingUsers = this.data.collaboration.operatingUsers.filter(user => user.userId !== userId)

    this.setData({
      'collaboration.onlineUsers': onlineUsers,
      'collaboration.operatingUsers': operatingUsers
    })

    // 显示用户离开提示
    wx.showToast({
      title: `${data.nickname || '协作者'}离开了协作`,
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 处理支出实时更新
   */
  handleExpenseUpdate(data) {
    const { planId, expenseData, openid, updateType } = data

    // 如果是自己的更新，忽略（避免重复更新）
    const userInfo = wx.getStorageSync('userInfo')
    if (openid === userInfo.openid) return

    console.log('处理支出实时更新:', updateType, expenseData)

    // 重新加载支出记录
    this.loadExpenseRecords()

    // 显示更新提示
    const operatorName = expenseData.operatorName || '协作者'
    let message = ''
    switch (updateType) {
      case 'add':
        message = `${operatorName}添加了新支出`
        break
      case 'delete':
        message = `${operatorName}删除了支出记录`
        break
      case 'update':
        message = `${operatorName}修改了支出记录`
        break
      default:
        message = `${operatorName}更新了支出记录`
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 处理协作消息
   */
  handleCollaborationMessage(data) {
    const { messageType, messageData, openid } = data

    console.log('收到协作消息:', messageType, messageData)

    // 根据消息类型处理
    switch (messageType) {
      case 'expense_operation':
        this.handleExpenseOperation(messageData)
        break
      case 'notification':
        wx.showToast({
          title: messageData.text || '收到协作通知',
          icon: 'none',
          duration: 2000
        })
        break
      default:
        console.log('未知的协作消息类型:', messageType)
    }
  },

  /**
   * 处理支出操作状态
   */
  handleExpenseOperation(messageData) {
    const { operation, userId, nickname, expenseId } = messageData

    if (operation === 'deleting') {
      // 显示正在删除状态
      const operatingUsers = [...this.data.collaboration.operatingUsers]
      const existingIndex = operatingUsers.findIndex(user => user.userId === userId)

      if (existingIndex >= 0) {
        operatingUsers[existingIndex] = { userId, nickname, operation: 'deleting', expenseId }
      } else {
        operatingUsers.push({ userId, nickname, operation: 'deleting', expenseId })
      }

      this.setData({
        'collaboration.operatingUsers': operatingUsers
      })

      // 3秒后自动清除状态
      setTimeout(() => {
        const currentOperatingUsers = this.data.collaboration.operatingUsers.filter(
          user => !(user.userId === userId && user.operation === 'deleting')
        )
        this.setData({
          'collaboration.operatingUsers': currentOperatingUsers
        })
      }, 3000)
    }
  },

  /**
   * 发送支出更新
   */
  sendExpenseUpdate(updateType, expenseData) {
    if (!this.data.collaboration.isConnected) return

    const userInfo = wx.getStorageSync('userInfo')
    cloudRunWebSocketManager.send({
      planId: this.data.planId,
      updateType,
      expenseData: {
        ...expenseData,
        operatorName: userInfo.nickName || '用户'
      }
    }, 'expense_update')
  },

  /**
   * 发送协作消息
   */
  sendCollaborationMessage(messageType, messageData) {
    if (!this.data.collaboration.isConnected) return

    cloudRunWebSocketManager.send({
      messageType,
      messageData
    }, 'collaboration_message')
  },

  /**
   * 页面隐藏时离开协作房间
   */
  onHide() {
    this.leaveExpenseCollaborationRoom()
  },

  /**
   * 页面卸载时清理WebSocket连接
   */
  onUnload() {
    // 离开协作房间
    this.leaveExpenseCollaborationRoom()

    // 关闭WebSocket连接
    cloudRunWebSocketManager.close()
  }
})
