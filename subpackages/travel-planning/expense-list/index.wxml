<!-- subpackages/travel-planning/expense-list/index.wxml -->

<!-- WXS模块：协作状态工具函数 -->
<wxs module="collaborationUtils">
// 判断用户是否在线
function isUserOnline(onlineUsers, userId) {
  if (!onlineUsers || !userId) return false;
  for (var i = 0; i < onlineUsers.length; i++) {
    if (onlineUsers[i].userId === userId) {
      return true;
    }
  }
  return false;
}

// 获取连接状态文本
function getConnectionStatusText(status) {
  if (status === 'connected') return '实时';
  if (status === 'connecting') return '连接中';
  if (status === 'error') return '连接失败';
  return '离线';
}

// 判断支出是否正在被操作
function isExpenseBeingOperated(operatingUsers, expenseId) {
  if (!operatingUsers || !expenseId) return false;
  for (var i = 0; i < operatingUsers.length; i++) {
    if (operatingUsers[i].expenseId === expenseId) {
      return true;
    }
  }
  return false;
}

module.exports = {
  isUserOnline: isUserOnline,
  getConnectionStatusText: getConnectionStatusText,
  isExpenseBeingOperated: isExpenseBeingOperated
};
</wxs>

<view class="page-container">
  <!-- 统计信息（增强协作状态显示） -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-label">总支出</text>
        <text class="stats-value">¥{{totalAmount}}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">记录数</text>
        <text class="stats-value">{{recordCount}}笔</text>
      </view>
      <!-- 协作状态显示 -->
      <view wx:if="{{collaboration.onlineUsers.length > 0}}" class="stats-item collaboration-status">
        <view class="collaboration-info">
          <view class="online-users">
            <view wx:for="{{collaboration.onlineUsers}}" wx:key="userId" class="online-user" wx:if="{{index < 3}}">
              <view class="user-avatar">
                <image wx:if="{{item.avatarUrl}}" src="{{item.avatarUrl}}" mode="aspectFill" />
                <view wx:else class="avatar-placeholder">{{item.nickname ? item.nickname.charAt(0) : 'U'}}</view>
                <view class="online-dot"></view>
              </view>
            </view>
            <view wx:if="{{collaboration.onlineUsers.length > 3}}" class="more-users">
              +{{collaboration.onlineUsers.length - 3}}
            </view>
          </view>
          <view class="connection-status {{collaboration.connectionStatus}}">
            <view class="connection-dot"></view>
            <text class="connection-text">{{collaborationUtils.getConnectionStatusText(collaboration.connectionStatus)}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 费用记录列表 -->
  <view class="content-section">

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading size="24px" color="#ffffff">加载中...</van-loading>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{isEmpty}}" class="empty-container">
      <van-empty description="暂无费用记录">
        <view slot="image" class="empty-icon">
          <custom-icon name="wallet" size="120" color="rgba(255,255,255,0.3)" />
        </view>
      </van-empty>
      <view class="empty-action">
        <van-button type="primary" size="normal" bind:click="addExpense" custom-class="add-first-btn">
          添加第一笔费用
        </van-button>
      </view>
    </view>

    <!-- 费用记录列表（增强协作状态显示） -->
    <view wx:else class="expense-list">
      <view wx:for="{{expenseRecords}}" wx:key="id" class="expense-item-wrapper">
        <!-- 正在操作状态提示 -->
        <view wx:if="{{collaborationUtils.isExpenseBeingOperated(collaboration.operatingUsers, item.id)}}" class="operating-overlay">
          <view class="operating-indicator">
            <view class="loading-spinner"></view>
            <text class="operating-text">正在删除...</text>
          </view>
        </view>

        <view class="expense-item {{collaborationUtils.isExpenseBeingOperated(collaboration.operatingUsers, item.id) ? 'operating' : ''}}" bind:tap="viewExpenseDetail" data-record="{{item}}">
          <view class="expense-info">
            <text class="expense-desc">{{item.description}}</text>
            <view class="expense-meta">
              <text class="expense-category">{{item.category}}</text>
              <text class="expense-date">{{item.date}}</text>
              <text wx:if="{{item.location}}" class="expense-location">{{item.location}}</text>
            </view>
          </view>
          <view class="expense-amount">
            <text class="amount">¥{{item.amount}}</text>
          </view>
        </view>
        <view class="expense-actions">
          <view class="delete-btn {{collaborationUtils.isExpenseBeingOperated(collaboration.operatingUsers, item.id) ? 'disabled' : ''}}" catch:tap="deleteExpenseRecord" data-record="{{item}}">
            <custom-icon name="delete" size="72" color="{{collaborationUtils.isExpenseBeingOperated(collaboration.operatingUsers, item.id) ? '#ccc' : '#ffffff'}}" />
          </view>
        </view>
      </view>
    </view>
  </view>

</view>
