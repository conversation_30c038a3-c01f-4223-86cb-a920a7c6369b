<!--subpackages/travel-planning/destination/index.wxml-->
<view class="destination-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="gradient-orb orb-1"></view>
    <view class="gradient-orb orb-2"></view>
    <view class="gradient-orb orb-3"></view>
  </view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">



    <!-- 快速选择 -->
    <view class="quick-actions-section">
      <view class="section-header">
        <text class="section-title">快速选择</text>
      </view>

      <view class="quick-actions">
        <view class="quick-action-item" bindtap="openMapPicker">
          <view class="action-icon location">
            <custom-icon name="map" size="24" color="#fff" />
          </view>
          <text class="action-title">地图选点</text>
          <text class="action-desc">在地图上选择位置</text>
        </view>
      </view>
    </view>

    <!-- 最近选择 -->
    <view wx:if="{{recentDestinations.length > 0}}" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近选择</text>
        <view class="section-action" bindtap="clearRecent">
          <text>清空</text>
        </view>
      </view>

      <view class="recent-list">
        <view
          wx:for="{{recentDestinations}}"
          wx:key="id"
          class="recent-item"
          bindtap="selectDestination"
          data-destination="{{item}}"
        >
          <view class="recent-icon">
            <custom-icon name="clock" size="16" color="#636e72" />
          </view>
          <view class="recent-info">
            <text class="recent-name">{{item.name}}</text>
            <text class="recent-desc">{{item.province}} {{item.country}}</text>
          </view>
        </view>
      </view>
    </view>



    <!-- 底部安全距离 -->
    <view class="safe-bottom"></view>
  </scroll-view>

  <!-- 加载提示 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>