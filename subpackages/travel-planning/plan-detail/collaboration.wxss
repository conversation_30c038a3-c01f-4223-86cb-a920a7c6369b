/* 协作功能样式 */

/* 实时编辑状态提示 */
.editing-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(82, 196, 26, 0.95);
  backdrop-filter: blur(10rpx);
  padding: 16rpx 32rpx;
  transform: translateY(-100%);
  animation: slideDown 0.3s ease-out forwards;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.pulse-dot {
  width: 16rpx;
  height: 16rpx;
  background: white;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.status-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

@keyframes slideDown {
  to {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 协作者头像显示 */
.collaborators-avatars {
  display: flex;
  align-items: center;
  gap: -8rpx;
  margin-right: 16rpx;
}

.avatar-item {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.collaborator-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  background: #f5f5f5;
  overflow: hidden;
  display: block;
}

.creator-avatar {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2rpx rgba(255, 77, 79, 0.3);
}

.creator-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #faad14;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.creator-ring {
  width: 48rpx;
  height: 48rpx;
  border: 3rpx solid #ff4d4f;
  border-radius: 50%;
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  animation: creatorPulse 2s infinite;
}

.more-collaborators {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(78, 205, 196, 0.2);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-count {
  font-size: 20rpx;
  color: #4ECDC4;
  font-weight: 600;
}

@keyframes creatorPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

/* 协作信息显示 */
.collaboration-info {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.collaboration-text {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.collab-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 邀请弹窗样式 */
.invite-popup {
  background: transparent !important;
}

.invite-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx 32rpx 0 0;
  padding: 48rpx 32rpx;
  min-height: 60vh;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 48rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3436;
}

.invite-content {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

/* 邀请码显示 */
.invite-code-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.code-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.15) 0%, rgba(69, 183, 209, 0.15) 100%);
  border-radius: 24rpx;
  border: 2rpx solid rgba(78, 205, 196, 0.3);
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(78, 205, 196, 0.1);
}

.code-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8rpx;
}

.code-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.invite-code {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3436;
  letter-spacing: 8rpx;
  font-family: 'Courier New', monospace;
  text-shadow: 0 2rpx 4rpx rgba(78, 205, 196, 0.2);
}

.copy-btn {
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  min-height: 64rpx;
}

.copy-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.4);
}

.copy-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

.code-hint {
  font-size: 26rpx;
  color: #74818a;
  font-weight: 500;
}

/* 邀请操作按钮 */
.invite-actions {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  align-items: center;
  margin-bottom: 48rpx;
}

.action-description {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  background: rgba(245, 245, 245, 0.8);
  border-radius: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

.link-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  height: 100rpx !important;
  width: 100% !important;
  border-radius: 28rpx !important;
  font-size: 34rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: white !important;
}

.link-btn:active {
  transform: scale(0.98) !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15) !important;
}

/* 当前协作者列表 */
.current-collaborators {
  background: rgba(245, 245, 245, 0.5);
  border-radius: 24rpx;
  padding: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.collaborators-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.collaborator-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
}

.collaborator-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  display: block;
}

.collaborator-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.collaborator-name {
  font-size: 28rpx;
  color: #2d3436;
  font-weight: 500;
}

.collaborator-role {
  font-size: 24rpx;
  color: #999;
}

.creator-crown {
  padding: 8rpx;
  background: rgba(250, 173, 20, 0.1);
  border-radius: 12rpx;
}

/* 在线状态指示器样式 */
.avatar-container {
  position: relative;
}

.online-indicator {
  position: absolute;
  bottom: -2rpx;
  right: -2rpx;
  width: 16rpx;
  height: 16rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  z-index: 3;
}

.online-dot {
  width: 10rpx;
  height: 10rpx;
  background: #52c41a;
  border-radius: 50%;
  animation: onlinePulse 2s infinite;
}

@keyframes onlinePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* WebSocket连接状态指示器 */
.connection-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
  margin-left: auto;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.1);
}

.connection-status.connected {
  background: rgba(82, 196, 26, 0.2);
}

.connection-status.connecting {
  background: rgba(250, 173, 20, 0.2);
}

.connection-status.error {
  background: rgba(255, 77, 79, 0.2);
}

.connection-status.disconnected {
  background: rgba(140, 140, 140, 0.2);
}

.connection-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #8c8c8c;
}

.connection-status.connected .connection-dot {
  background: #52c41a;
  animation: connectionPulse 2s infinite;
}

.connection-status.connecting .connection-dot {
  background: #faad14;
  animation: connectionBlink 1s infinite;
}

.connection-status.error .connection-dot {
  background: #ff4d4f;
}

.connection-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.connection-status.connected .connection-text {
  color: rgba(82, 196, 26, 0.9);
}

.connection-status.connecting .connection-text {
  color: rgba(250, 173, 20, 0.9);
}

.connection-status.error .connection-text {
  color: rgba(255, 77, 79, 0.9);
}

@keyframes connectionPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes connectionBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}
