// subpackages/travel-planning/create-plan/index.js

import travelService from '../../../utils/travel-service.js'
import userManager from '../../../utils/userManager.js'

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      destination: '',
      coordinates: null,
      startDate: '',
      endDate: '',
      budget: '',
      notes: ''
    },

    // 人数选择器数据
    participantOptions: ['1人', '2人', '3人', '4人', '5人', '6人', '7人', '8人', '9人', '10人', '10人以上'],
    participantIndex: 0,

    // 计算属性
    duration: 0,
    today: '',

    // 表单验证
    formValidation: {
      canSubmit: false,
      completeness: 0,
      hasErrors: false,
      errors: []
    },

    // 状态
    loading: false,
    loadingText: '',
    saving: false,

    // 智能规划相关
    smartPlanData: {
      destination: '',
      duration: 3,
      preferences: [],
      startPoint: '', // 新增：起点
      endPoint: ''    // 新增：终点
    },
    smartPlanning: false,
    smartPlanResult: null,

    // 目的地搜索相关
    showDestinationSuggestions: false,
    destinationSuggestions: [],
    searchTimer: null,

    // 魔法解析相关
    showMagicModal: false,
    magicUrl: '',
    magicParsing: false,
    magicResult: null,

    // 魔法解析相关
    magicInput: '',
    magicParsing: false,
    parseResult: null,
    supportedPlatforms: [
      { name: '小红书', icon: '📱' },
      { name: '微信公众号', icon: '💬' },
      { name: '大众点评', icon: '⭐' },
      { name: '马蜂窝', icon: '🐝' }
    ],

    // {{ AURA-X: Modify - 改为邀请选项状态管理. Approval: 寸止(ID:1738056000). }}
    // 邀请协作状态
    enableCollaboration: false,
    showInviteOption: false,

    // {{ AURA-X: Add - 编辑模式状态管理. Approval: 寸止(ID:1738056000). }}
    // 编辑模式
    isEditMode: false,
    planId: null,

    // 地点输入弹窗相关
    showLocationModal: false
  },

  // {{ AURA-X: Modify - 支持编辑模式，处理mode=edit参数. Approval: 寸止(ID:1738056000). }}
  onLoad(options) {
    // 检查是否为编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        isEditMode: true,
        planId: options.id
      })
      this.loadPlanForEdit(options.id)
    } else {
      this.initPage()

      // 根据模式参数自动显示相应界面
      if (options.mode === 'magic') {
        // 自动显示魔法解析界面
        setTimeout(() => {
          this.showMagicParse()
        }, 500)
      } else if (options.mode === 'ai') {
        // 自动显示地点输入弹窗
        setTimeout(() => {
          this.showLocationModal()
        }, 500)
      } else if (options.mode === 'manual') {
        // 自动跳转到手动创建步骤
        this.setData({
          currentStep: 1
        })
      }
    }
  },

  // 初始化页面
  initPage() {
    const today = new Date()
    const todayStr = this.formatDate(today)

    this.setData({
      today: todayStr
    })

    this.validateForm()
  },

  // 智能表单验证
  validateForm() {
    const { formData, participantIndex } = this.data
    const errors = []
    let completeness = 0
    const totalFields = 4 // 必填字段数量

    // 验证标题
    if (!formData.title || formData.title.trim().length === 0) {
      errors.push({ field: 'title', message: '请输入计划标题' })
    } else {
      completeness += 25
    }

    // 验证目的地
    if (!formData.destination || formData.destination.trim().length === 0) {
      errors.push({ field: 'destination', message: '请选择目的地' })
    } else {
      completeness += 25
    }

    // 验证出发日期
    if (!formData.startDate) {
      errors.push({ field: 'startDate', message: '请选择出发日期' })
    } else {
      completeness += 25
    }

    // 验证返回日期
    if (!formData.endDate) {
      errors.push({ field: 'endDate', message: '请选择返回日期' })
    } else if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      if (endDate <= startDate) {
        errors.push({ field: 'endDate', message: '返回日期必须晚于出发日期' })
      } else {
        completeness += 25
      }
    }

    // 计算行程天数
    let duration = 0
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1
    }

    // 更新验证状态
    this.setData({
      duration: duration,
      formValidation: {
        canSubmit: errors.length === 0,
        completeness: Math.round(completeness),
        hasErrors: errors.length > 0,
        errors: errors
      }
    })
  },

  // {{ AURA-X: Add - 加载计划数据用于编辑模式. Approval: 寸止(ID:1738056000). }}
  // 加载计划数据用于编辑
  async loadPlanForEdit(planId) {
    try {
      // {{ AURA-X: Add - 设置编辑模式的页面标题. Approval: 寸止(ID:1738056000). }}
      wx.setNavigationBarTitle({
        title: '编辑旅行计划'
      })

      this.setData({ loading: true, loadingText: '加载计划数据...' })

      // {{ AURA-X: Modify - 修复方法名错误，使用正确的getTravelPlan方法. Approval: 寸止(ID:1738056000). }}
      const result = await travelService.getTravelPlan(planId)

      if (result.success && result.data) {
        const planData = result.data

        // 填充表单数据
        this.setData({
          'formData.title': planData.title || '',
          'formData.destination': planData.destination || '',
          'formData.coordinates': planData.coordinates || null,
          'formData.startDate': planData.startDate || '',
          'formData.endDate': planData.endDate || '',
          'formData.budget': planData.budget?.toString() || '',
          'formData.notes': planData.notes || '',
          participantIndex: Math.max(0, Math.min(planData.participants - 1, 10)) || 0,
          enableCollaboration: planData.enableCollaboration || false
        })

        // 初始化其他数据
        const today = new Date()
        this.setData({
          today: this.formatDate(today)
        })

        this.checkCanNextStep()
      } else {
        wx.showToast({
          title: '加载计划失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载计划数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false, loadingText: '' })
    }
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 计算旅行天数
  calculateDuration() {
    const { startDate, endDate } = this.data.formData
    if (startDate && endDate) {
      const start = new Date(startDate)
      const end = new Date(endDate)
      const diffTime = Math.abs(end - start)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
      this.setData({ duration: diffDays })
    } else {
      this.setData({ duration: 0 })
    }
  },

  // 检查是否可以进入下一步
  checkCanNextStep() {
    const { currentStep, formData } = this.data
    let canNext = false
    
    if (currentStep === 1) {
      canNext = formData.title.trim() && formData.destination.trim()
    } else if (currentStep === 2) {
      canNext = formData.startDate && formData.endDate
    } else if (currentStep === 3) {
      canNext = true
    }
    
    this.setData({ canNextStep: canNext })
  },

  // 输入处理
  onTitleInput(e) {
    this.setData({
      'formData.title': e.detail.value
    })
    this.validateForm()
  },

  onBudgetInput(e) {
    this.setData({
      'formData.budget': e.detail.value
    })
  },

  onNotesInput(e) {
    this.setData({
      'formData.notes': e.detail.value
    })
  },

  // 日期选择
  onStartDateChange(e) {
    this.setData({
      'formData.startDate': e.detail.value
    })
    this.validateForm()
  },

  onEndDateChange(e) {
    this.setData({
      'formData.endDate': e.detail.value
    })
    this.validateForm()
  },

  // 人数选择器变化
  onParticipantChange(e) {
    const index = e.detail.value
    const participantText = this.data.participantOptions[index]
    // 提取数字部分进行比较
    const count = parseInt(participantText) || 1
    this.setData({
      participantIndex: index,
      // 人数≥2时自动开启协作
      enableCollaboration: count >= 2
    })

    // 显示协作状态提示
    if (count >= 2) {
      console.log('已自动开启协作功能，人数:', count)
    }
  },

  // {{ AURA-X: Modify - 直接显示真实邀请码弹窗. Approval: 寸止(ID:1738056000). }}
  // 显示邀请选项
  showInviteOption() {
    // 直接开启协作
    this.setData({
      enableCollaboration: true,
      showInviteOption: true
    })

    // 生成真实邀请码
    const inviteCode = this.generateTempInviteCode()
    const planTitle = this.data.formData.title || '我的旅行计划'

    // 调用与快速操作区相同的邀请弹窗
    this.showInviteModal(inviteCode, null, planTitle)

    wx.showToast({
      title: '已开启协作功能',
      icon: 'success'
    })
  },

  // 生成临时邀请码
  generateTempInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // 选择目的地
  selectDestination() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/destination/index?mode=select',
      events: {
        selectDestination: (data) => {
          this.setData({
            'formData.destination': data.name,
            'formData.coordinates': data.coordinates
          })
          this.validateForm()
        }
      }
    })
  },

  // 地图选点 - 优化权限处理
  openLocationPicker() {
    // 先检查权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户拒绝过权限，引导用户手动开启
          wx.showModal({
            title: '位置权限',
            content: '地图选点需要位置权限，请在设置中开启',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting()
              }
            }
          })
          return
        }

        wx.chooseLocation({
          success: (res) => {
            this.setData({
              'formData.destination': res.name || res.address || '未知地点',
              'formData.coordinates': {
                latitude: res.latitude,
                longitude: res.longitude
              }
            })
            this.validateForm()
          },
          fail: (err) => {
            console.error('选择位置失败:', err)

            if (err.errMsg.includes('cancel')) {
              return // 用户取消，不显示错误
            }

            if (err.errMsg.includes('auth')) {
              wx.showModal({
                title: '位置权限',
                content: '地图选点需要位置权限，请允许位置权限',
                confirmText: '重新授权',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    this.openLocationPicker()
                  }
                }
              })
            } else {
              wx.showToast({
                title: '选择位置失败，请重试',
                icon: 'none'
              })
            }
          }
        })
      }
    })
  },

  // 地点选择 - 重命名避免冲突
  openLocationModal() {
    this.setData({
      showLocationModal: true
    })
  },

  // 显示地点输入弹窗
  showLocationModal() {
    this.setData({
      showLocationModal: true
    })
  },

  // 隐藏地点输入弹窗
  hideLocationModal() {
    this.setData({
      showLocationModal: false
    })
  },

  // 选择地点回调
  onLocationSelect(e) {
    const location = e.detail.location

    this.setData({
      'formData.destination': location.name,
      'formData.coordinates': location.location ? {
        latitude: location.location.latitude,
        longitude: location.location.longitude
      } : null,
      showLocationModal: false
    })

    this.validateForm()
    console.log('选择的地点:', location)
  },

  // 保存计划
  async savePlan() {
    if (this.data.saving) return

    const self = this
    // {{ AURA-X: Modify - 根据编辑模式显示不同的加载文本. Approval: 寸止(ID:1738056000). }}
    this.setData({
      saving: true,
      loading: true,
      loadingText: this.data.isEditMode ? '正在更新计划...' : '正在创建计划...'
    })

    // 计算参与人数 - 确保数据类型正确
    const participantIndex = Number(this.data.participantIndex) || 0
    const participantCount = participantIndex + 1 // 索引从0开始，所以+1



    // 获取当前用户信息
    const userInfo = userManager.getUserInfo()

    // 如果用户头像是临时文件，先上传到云存储
    if (userInfo?.avatar && userInfo.avatar.startsWith('wxfile://')) {
      try {
        const uploadResult = await wx.cloud.callFunction({
          name: 'travel',
          data: {
            action: 'uploadAvatar',
            data: { tempFilePath: userInfo.avatar }
          }
        })

        if (uploadResult.result.success) {
          // 更新本地用户信息
          userInfo.avatar = uploadResult.result.data.avatarUrl
          userManager.updateUserInfo(userInfo)
        }
      } catch (error) {
        console.error('上传头像失败:', error)
      }
    }

    const planData = Object.assign({}, this.data.formData, {
      duration: this.data.duration,
      participantCount: participantCount,
      enableCollaboration: this.data.enableCollaboration
    })

    // {{ AURA-X: Modify - 根据编辑模式选择创建或更新计划. Approval: 寸止(ID:1738056000). }}
    // 使用云函数创建或更新计划（支持协作功能）
    const requestData = {
      name: 'travel',
      data: {
        action: self.data.isEditMode ? 'updateTravelPlan' : 'addTravelPlan',
        data: self.data.isEditMode ? { ...planData, id: self.data.planId } : planData
      }
    }

    wx.cloud.callFunction(requestData).then(function(result) {
      self.setData({
        saving: false,
        loading: false
      })

      if (result.result.success) {
        // {{ AURA-X: Modify - 根据编辑模式显示不同的成功提示. Approval: 寸止(ID:1738056000). }}
        wx.showToast({
          title: self.data.isEditMode ? '更新成功' : '创建成功',
          icon: 'success'
        })

        // 构建新计划数据用于缓存更新
        const newPlanData = {
          _id: result.result.data.id || result.result.data.planId,
          ...planData,
          _openid: wx.cloud.OPENID,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
          status: self.determineInitialStatus(planData.startDate),
          // 添加协作信息
          collaboration: {
            enabled: self.data.enableCollaboration,
            inviteCode: result.result.data.inviteCode || null,
            creator: wx.cloud.OPENID,
            collaborators: [],
            maxCollaborators: 5
          }
        }

        // 通知旅行规划页面刷新数据，并传递新计划数据
        self.notifyTravelPageRefresh(newPlanData)

        // {{ AURA-X: Modify - 创建成功后直接返回，不弹窗. Approval: 寸止(ID:1738056000). }}
        // 创建成功后直接返回，不显示邀请弹窗
        setTimeout(function() {
          wx.navigateBack()
        }, 1500)
      } else {
        console.error(self.data.isEditMode ? '更新计划失败:' : '创建计划失败:', result.result.message)
        wx.showToast({
          title: result.result.message || (self.data.isEditMode ? '更新失败' : '创建失败'),
          icon: 'none'
        })
      }
    }).catch(function(error) {
      self.setData({
        saving: false,
        loading: false
      })
      console.error('创建计划异常:', error)
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      })
    })
  },

  // 取消创建
  cancelCreate() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消创建计划吗？已填写的信息将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 创建旅行计划',
      path: '/subpackages/travel-planning/create-plan/index'
    }
  },

  // 通知旅行规划页面刷新
  notifyTravelPageRefresh(newPlanData = null) {
    try {
      const simpleDataManager = require('../../../utils/simple-data-manager.js').default

      // 如果有新计划数据，立即更新本地缓存
      if (newPlanData) {
        // 获取现有缓存数据
        const existingPlans = simpleDataManager.getFromLocalStorage('travel_plans_cache') || []

        // 添加新计划到缓存顶部
        const updatedPlans = [newPlanData, ...existingPlans]

        // 更新本地缓存
        simpleDataManager.setLocalStorage('travel_plans_cache', updatedPlans)
        simpleDataManager.setMemoryCache('travel_plans_cache', updatedPlans)

        // 通知数据变更监听器
        simpleDataManager.notifyChange('travel_plans', updatedPlans)

        console.log('新计划已添加到本地缓存:', newPlanData.title)
      } else {
        // 清理缓存，强制从云端重新获取
        simpleDataManager.clearCache('travel')
      }

      // 设置全局刷新标记
      wx.setStorageSync('travel_page_needs_refresh', Date.now())
    } catch (error) {
      console.error('通知页面刷新失败:', error)
      // 静默处理通知失败
    }
  },

  // {{ AURA-X: Add - 创建成功后的邀请弹窗，会自动返回. Approval: 寸止(ID:1738056000). }}
  // 显示创建成功后的邀请弹窗
  showCreateSuccessInviteModal(inviteCode, planId, planTitle) {
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    wx.showModal({
      title: '邀请好友协作',
      content: `计划创建成功！邀请码：${inviteCode}\n\n点击"复制邀请"将邀请信息复制到剪贴板，发送给好友即可邀请协作。`,
      confirmText: '复制邀请',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
        // 无论选择什么都返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    })
  },

  // 显示邀请弹窗（创建过程中）
  showInviteModal(inviteCode, planId, planTitle) {
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    // {{ AURA-X: Modify - 修改按钮文字和行为，取消时不返回页面. Approval: 寸止(ID:1738056000). }}
    wx.showModal({
      title: '邀请好友协作',
      content: `邀请码：${inviteCode}\n\n点击"复制邀请"将邀请信息复制到剪贴板，发送给好友即可邀请协作。`,
      confirmText: '复制邀请',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
        // 点击取消时不做任何操作，用户可以继续创建计划
      }
    })
  },

  // 智能规划相关方法
  // 选择目的地（智能规划）
  selectDestinationForSmart() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/destination/index?mode=smart-plan',
      events: {
        selectDestination: (data) => {
          this.setData({
            'smartPlanData.destination': data.name
          })
        }
      }
    })
  },

  // 选择天数
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.duration)
    this.setData({
      'smartPlanData.duration': duration
    })
  },

  // 自定义天数
  selectCustomDuration() {
    wx.showModal({
      title: '自定义天数',
      content: '请输入旅行天数（1-30天）',
      editable: true,
      placeholderText: '输入天数',
      success: (res) => {
        if (res.confirm && res.content) {
          const duration = parseInt(res.content)
          if (duration >= 1 && duration <= 30) {
            this.setData({
              'smartPlanData.duration': duration
            })
          } else {
            wx.showToast({
              title: '请输入1-30之间的数字',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 切换偏好
  togglePreference(e) {
    const type = e.currentTarget.dataset.type
    const preferences = [...this.data.smartPlanData.preferences]
    const index = preferences.indexOf(type)

    if (index > -1) {
      preferences.splice(index, 1)
    } else {
      preferences.push(type)
    }

    this.setData({
      'smartPlanData.preferences': preferences
    })
  },

  // 执行智能规划
  async performSmartPlanning() {
    const { destination, duration, preferences, startPoint, endPoint } = this.data.smartPlanData

    if (!destination) {
      wx.showToast({
        title: '请选择目的地',
        icon: 'none'
      })
      return
    }

    this.setData({ smartPlanning: true })

    try {
      // 调用AI智能规划云函数，增加超时处理
      const result = await Promise.race([
        wx.cloud.callFunction({
          name: 'ai-travel',
          data: {
            action: 'smartPlanning',
            data: {
              destination: destination,
              duration: duration,
              preferences: preferences,
              startPoint: startPoint,
              endPoint: endPoint
            }
          }
        }),
        new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('请求超时，请稍后重试'))
          }, 15000) // 15秒超时
        })
      ])

      if (result.result.success) {
        const planData = result.result.data

        this.setData({
          smartPlanResult: planData,
          smartPlanning: false
        })

        // 触觉反馈
        wx.vibrateShort({ type: 'light' })

        wx.showToast({
          title: '🎯 规划完成',
          icon: 'success'
        })
      } else {
        throw new Error(result.result.message || '智能规划失败')
      }

    } catch (error) {
      console.error('智能规划失败:', error)
      this.setData({ smartPlanning: false })

      // 直接报错，不做降级处理
      wx.showModal({
        title: '智能规划失败',
        content: `错误信息：${error.message}`,
        showCancel: false
      })
    }
  },

  // 查看完整地图
  viewFullMap() {
    if (!this.data.smartPlanResult || !this.data.smartPlanResult.mapData) {
      wx.showToast({
        title: '地图数据不可用',
        icon: 'none'
      })
      return
    }

    // 跳转到地图详情页面
    wx.navigateTo({
      url: `/subpackages/travel-planning/map-view/index?data=${encodeURIComponent(JSON.stringify(this.data.smartPlanResult.mapData))}`
    })
  },

  // 编辑智能规划结果
  editSmartPlan() {
    wx.showModal({
      title: '编辑行程',
      content: '此功能正在开发中，您可以在创建后进入详情页面进行编辑',
      showCancel: false
    })
  },

  // 应用智能规划结果
  applySmartPlan() {
    const result = this.data.smartPlanResult

    if (!result) return

    // 填充表单数据
    const formData = {
      title: result.title || '',
      destination: result.destination || '',
      budget: result.estimatedCost?.total?.toString() || '',
      notes: this.generateNotesFromSmartPlan(result)
    }

    // 设置日期
    const startDate = new Date()
    const endDate = new Date(startDate.getTime() + (result.duration - 1) * 24 * 60 * 60 * 1000)

    this.setData({
      'formData.title': formData.title,
      'formData.destination': formData.destination,
      'formData.budget': formData.budget,
      'formData.notes': formData.notes,
      'formData.startDate': this.formatDate(startDate),
      'formData.endDate': this.formatDate(endDate),
      currentStep: 1 // 跳转到基本信息步骤
    })

    // 保存智能规划结果供后续使用
    this.smartPlanData = result

    wx.showToast({
      title: '已应用智能规划',
      icon: 'success'
    })
  },

  // 从智能规划结果生成备注
  generateNotesFromSmartPlan(result) {
    let notes = ''

    if (result.preferences && result.preferences.length > 0) {
      notes += '🎯 旅行偏好：\n'
      const preferenceTexts = {
        culture: '文化历史',
        nature: '自然风光',
        food: '美食体验',
        shopping: '购物娱乐'
      }
      result.preferences.forEach(pref => {
        const text = preferenceTexts[pref] || pref
        notes += `• ${text}\n`
      })
    }

    if (result.attractions && result.attractions.length > 0) {
      notes += '\n📍 推荐景点：\n'
      result.attractions.slice(0, 5).forEach(attraction => {
        notes += `• ${attraction.name}${attraction.reason ? ` - ${attraction.reason}` : ''}\n`
      })
    }

    if (result.estimatedCost) {
      notes += `\n💰 预估费用：¥${result.estimatedCost.total}\n`
      notes += `• 门票：¥${result.estimatedCost.tickets}\n`
      notes += `• 餐饮：¥${result.estimatedCost.meals}\n`
      notes += `• 交通：¥${result.estimatedCost.transport}\n`
      if (result.estimatedCost.accommodation > 0) {
        notes += `• 住宿：¥${result.estimatedCost.accommodation}\n`
      }
    }

    return notes.trim()
  },





  // 显示魔法解析弹窗
  showMagicParse() {
    this.setData({
      showMagicModal: true,
      magicUrl: '',
      magicResult: null
    })

    // 自动检测剪贴板内容
    this.checkClipboard()
  },

  // 隐藏魔法解析弹窗
  hideMagicModal() {
    this.setData({
      showMagicModal: false,
      magicUrl: '',
      magicResult: null,
      magicParsing: false
    })
  },

  // 魔法URL输入
  onMagicUrlInput(e) {
    this.setData({
      magicUrl: e.detail.value.trim()
    })
  },

  // 从剪贴板粘贴
  pasteFromClipboard() {
    const self = this
    wx.getClipboardData({
      success: function(res) {
        const clipboardData = res.data.trim()
        if (self.isValidUrl(clipboardData)) {
          self.setData({
            magicUrl: clipboardData
          })
          wx.showToast({
            title: '已粘贴链接',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: '剪贴板中没有有效链接',
            icon: 'none'
          })
        }
      },
      fail: function() {
        wx.showToast({
          title: '无法访问剪贴板',
          icon: 'none'
        })
      }
    })
  },

  // 清空魔法URL
  clearMagicUrl() {
    this.setData({
      magicUrl: '',
      magicResult: null
    })
  },

  // 检查剪贴板内容
  checkClipboard() {
    const self = this
    wx.getClipboardData({
      success: function(res) {
        const clipboardData = res.data.trim()
        if (self.isValidUrl(clipboardData) && self.isSupportedPlatform(clipboardData)) {
          // 如果剪贴板有支持的链接，自动填入
          self.setData({
            magicUrl: clipboardData
          })
        }
      },
      fail: function() {
        // 静默失败，不影响用户体验
      }
    })
  },

  // 验证URL格式
  isValidUrl(url) {
    const urlPattern = /^https?:\/\/.+/
    return urlPattern.test(url)
  },

  // 检查是否为支持的平台
  isSupportedPlatform(url) {
    const supportedDomains = [
      'xiaohongshu.com',
      'xhslink.com',
      'mp.weixin.qq.com',
      'weibo.com',
      'weibo.cn',
      'dianping.com',
      'mafengwo.cn'
    ]
    return supportedDomains.some(domain => url.includes(domain))
  },

  // 执行魔法解析
  async performMagicParse() {
    const { magicUrl } = this.data

    if (!magicUrl) {
      wx.showToast({
        title: '请输入链接',
        icon: 'none'
      })
      return
    }

    if (!this.isValidUrl(magicUrl)) {
      wx.showToast({
        title: '请输入有效的链接',
        icon: 'none'
      })
      return
    }

    this.setData({ magicParsing: true })

    try {
      // 调用AI云函数进行魔法解析
      const result = await wx.cloud.callFunction({
        name: 'ai-travel',
        data: {
          action: 'parseAndExtract',
          data: {
            url: magicUrl
          }
        }
      })

      if (result.result.success) {
        const parseData = result.result.data

        this.setData({
          magicResult: parseData,
          magicParsing: false
        })

        // 触觉反馈
        wx.vibrateShort({ type: 'light' })

        // 显示成功提示
        wx.showToast({
          title: '✨ 解析成功',
          icon: 'success'
        })

        // 延迟一下，然后应用解析结果
        setTimeout(() => {
          this.applyMagicResult(parseData)
        }, 1500)

      } else {
        throw new Error(result.result.message || '解析失败')
      }
    } catch (error) {
      console.error('魔法解析失败:', error)
      this.setData({ magicParsing: false })

      wx.showToast({
        title: error.message || '解析失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 应用魔法解析结果
  applyMagicResult(parseData) {
    const { title, aiParsed, locations } = parseData

    // 隐藏魔法弹窗
    this.hideMagicModal()

    // 如果有AI解析的结构化数据，直接应用
    if (aiParsed && aiParsed.destinations && aiParsed.destinations.length > 0) {
      // 提取目的地信息
      const destinations = aiParsed.destinations
      const firstDestination = destinations[0]

      // 设置表单数据
      this.setData({
        'formData.title': aiParsed.title || title || '魔法解析行程',
        'formData.destination': this.extractMainDestination(destinations),
        currentStep: 1 // 跳转到表单填写步骤
      })

      // 显示解析结果提示
      wx.showModal({
        title: '魔法解析完成',
        content: `已解析出 ${destinations.length} 个地点，${locations.length} 个位置信息。是否继续完善计划详情？`,
        confirmText: '继续',
        cancelText: '重新解析',
        success: (res) => {
          if (!res.confirm) {
            // 用户选择重新解析，重新显示魔法弹窗
            this.showMagicParse()
          }
        }
      })
    } else {
      // 如果没有结构化数据，至少设置标题和提取的地点
      const mainDestination = this.extractDestinationFromLocations(locations)

      this.setData({
        'formData.title': title || '魔法解析行程',
        'formData.destination': mainDestination,
        currentStep: 1
      })

      wx.showToast({
        title: `已提取 ${locations.length} 个地点`,
        icon: 'success'
      })
    }
  },

  // 从目的地列表中提取主要目的地
  extractMainDestination(destinations) {
    if (!destinations || destinations.length === 0) return ''

    // 寻找最常出现的城市名
    const cityCount = {}
    destinations.forEach(dest => {
      if (dest.name) {
        // 简单的城市名提取逻辑
        const cityMatch = dest.name.match(/([^市区县]+[市区县]?)/)
        if (cityMatch) {
          const city = cityMatch[1]
          cityCount[city] = (cityCount[city] || 0) + 1
        }
      }
    })

    // 返回出现次数最多的城市
    const sortedCities = Object.entries(cityCount).sort((a, b) => b[1] - a[1])
    return sortedCities.length > 0 ? sortedCities[0][0] : destinations[0].name || ''
  },

  // 从地点列表中提取目的地
  extractDestinationFromLocations(locations) {
    if (!locations || locations.length === 0) return ''

    // 寻找地址中的城市信息
    for (const location of locations) {
      if (location.text) {
        const cityMatch = location.text.match(/([^省市区县]+[市区县])/)
        if (cityMatch) {
          return cityMatch[1]
        }
      }
    }

    return ''
  },

  // 旧的链接导入方法已被魔法解析替代
  // showLinkImport 方法已移除，使用 showMagicParse 替代

  // 目的地输入处理
  onDestinationInput(e) {
    const value = e.detail.value
    this.setData({
      'smartPlanData.destination': value
    })

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    // 设置新的搜索定时器，防抖处理
    const timer = setTimeout(() => {
      if (value.trim()) {
        this.searchDestinations(value)
      } else {
        this.setData({
          showDestinationSuggestions: false,
          destinationSuggestions: []
        })
      }
    }, 300)

    this.setData({
      searchTimer: timer
    })
  },

  // 新增：选择热门目的地
  selectHotDestination(e) {
    const destination = e.currentTarget.dataset.destination
    this.setData({
      'smartPlanData.destination': destination,
      showDestinationSuggestions: false
    })
  },

  // 新增：起点输入处理
  onStartPointInput(e) {
    const value = e.detail.value
    this.setData({
      'smartPlanData.startPoint': value
    })
  },

  // 新增：终点输入处理
  onEndPointInput(e) {
    const value = e.detail.value
    this.setData({
      'smartPlanData.endPoint': value
    })
  },

  // 快速开始功能 - 直接打开地点输入弹窗
  quickStart(e) {
    const type = e.currentTarget.dataset.type

    // 直接打开地点输入弹窗
    this.setData({
      showLocationModal: true
    })
  },

  // 目的地输入框获得焦点
  onDestinationFocus() {
    if (this.data.smartPlanData.destination) {
      this.searchDestinations(this.data.smartPlanData.destination)
    }
  },

  // 目的地输入框失去焦点
  onDestinationBlur() {
    // 延迟隐藏建议列表，给用户点击建议的时间
    setTimeout(() => {
      this.setData({
        showDestinationSuggestions: false
      })
    }, 200)
  },

  // 搜索目的地
  async searchDestinations(keyword) {
    if (!keyword || keyword.trim().length < 2) {
      this.setData({
        showDestinationSuggestions: false,
        destinationSuggestions: []
      })
      return
    }

    try {
      // 调用destinations云函数进行地点搜索
      const result = await wx.cloud.callFunction({
        name: 'destinations',
        data: {
          action: 'searchDestinations',
          data: {
            keyword: keyword.trim(),
            limit: 8
          }
        }
      })

      if (result.result.success && result.result.data.length > 0) {
        // 处理搜索结果
        const suggestions = result.result.data
          .slice(0, 8)
          .map((item, index) => ({
            id: item._id || index,
            name: item.name,
            district: item.province || item.region || '中国',
            location: {
              latitude: item.latitude || 39.9042,
              longitude: item.longitude || 116.4074
            },
            type: item.category || 'destination'
          }))

        this.setData({
          destinationSuggestions: suggestions,
          showDestinationSuggestions: suggestions.length > 0
        })
      } else {
        this.setData({
          showDestinationSuggestions: false,
          destinationSuggestions: []
        })
      }
    } catch (error) {
      console.error('搜索目的地失败:', error)
      this.setData({
        showDestinationSuggestions: false,
        destinationSuggestions: []
      })
    }
  },

  // 选择搜索建议
  selectDestinationSuggestion(e) {
    const suggestion = e.currentTarget.dataset.suggestion

    this.setData({
      'smartPlanData.destination': suggestion.name,
      showDestinationSuggestions: false,
      destinationSuggestions: []
    })

    // 保存选中的位置信息供后续使用
    this.selectedDestination = {
      name: suggestion.name,
      location: suggestion.location,
      district: suggestion.district,
      type: suggestion.type
    }
  },

  // 跳过到手动创建
  skipToManualCreate() {
    this.setData({
      currentStep: 1,
      smartPlanResult: null
    })
  },

  // 显示地点输入弹窗
  showLocationModal() {
    this.setData({
      showLocationModal: true
    })
  },

  // 选择地点回调 - 智能规划用
  onLocationSelectForSmart(e) {
    const location = e.detail.location

    this.setData({
      'smartPlanData.destination': location.name,
      showLocationModal: false
    })

    console.log('选择的地点:', location)
  },

  // 智能状态判断
  determineInitialStatus(startDate) {
    if (!startDate) return 'planning'

    const today = new Date()
    const start = new Date(startDate)

    // 清除时间部分，只比较日期
    today.setHours(0, 0, 0, 0)
    start.setHours(0, 0, 0, 0)

    if (start.getTime() === today.getTime()) {
      return 'ongoing'
    } else if (start > today) {
      return 'planning'
    } else {
      return 'completed'
    }
  }
})
