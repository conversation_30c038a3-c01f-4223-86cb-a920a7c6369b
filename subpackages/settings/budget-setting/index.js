// subpackages/settings/budget-setting/index.js

// {{ AURA-X: Modify - 使用simpleDataManager替代不存在的dataManager. Approval: 寸止(ID:1738056000). }}
import simpleDataManager from '../../../utils/simple-data-manager.js'
import auth from '../../../utils/auth.js'

Page({
  data: {
    budget: {
      monthly: '',
      daily: '',
      travel: ''
    },
    loading: false,
    monthlyFocus: false,
    showTips: true,
    errorMessage: ''
  },

  onLoad() {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }

    this.loadUserBudget()
  },

  // 加载用户当前预算设置
  async loadUserBudget() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'expense',
        data: { action: 'getUserBudget' }
      })

      if (result.result && result.result.success) {
        this.setData({
          budget: {
            monthly: result.result.data.monthly || '',
            daily: result.result.data.daily || '',
            travel: result.result.data.travel || ''
          }
        })
      }
    } catch (error) {
      console.error('加载预算设置失败:', error)
    }
  },

  // 月度预算输入
  onMonthlyBudgetChange(e) {
    const value = e.detail.value
    this.setData({
      'budget.monthly': value,
      errorMessage: ''
    })
    this.validateBudget()
  },

  // 日常预算输入
  onDailyBudgetChange(e) {
    const value = e.detail.value
    this.setData({
      'budget.daily': value,
      errorMessage: ''
    })
    this.validateBudget()
  },

  // 旅行预算输入
  onTravelBudgetChange(e) {
    const value = e.detail.value
    this.setData({
      'budget.travel': value,
      errorMessage: ''
    })
    this.validateBudget()
  },

  // 验证预算设置
  validateBudget() {
    const { monthly, daily, travel } = this.data.budget
    
    if (!monthly || !daily || !travel) {
      return true // 输入未完成，不显示错误
    }

    const monthlyNum = parseFloat(monthly)
    const dailyNum = parseFloat(daily)
    const travelNum = parseFloat(travel)

    if (monthlyNum <= 0) {
      this.setData({ errorMessage: '月度预算必须大于0' })
      return false
    }

    if (dailyNum <= 0) {
      this.setData({ errorMessage: '日常预算必须大于0' })
      return false
    }

    if (travelNum <= 0) {
      this.setData({ errorMessage: '旅行预算必须大于0' })
      return false
    }

    if (dailyNum + travelNum > monthlyNum) {
      this.setData({ errorMessage: '日常预算和旅行预算之和不能超过月度预算' })
      return false
    }

    this.setData({ errorMessage: '' })
    return true
  },

  // 保存预算设置
  async saveBudget() {
    if (!this.validateBudget()) {
      return
    }

    const { monthly, daily, travel } = this.data.budget

    if (!monthly || !daily || !travel) {
      this.setData({ errorMessage: '请填写完整的预算信息' })
      return
    }

    this.setData({ loading: true })

    try {
      const result = await wx.cloud.callFunction({
        name: 'expense',
        data: {
          action: 'updateUserBudget',
          data: {
            monthly: parseFloat(monthly),
            daily: parseFloat(daily),
            travel: parseFloat(travel)
          }
        }
      })

      if (result.result && result.result.success) {
        wx.showToast({
          title: '预算设置成功',
          icon: 'success',
          duration: 2000
        })

        // 触觉反馈
        wx.vibrateShort()

        // 延迟返回
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        this.setData({ errorMessage: result.message || '保存失败' })
      }
    } catch (error) {
      console.error('保存预算失败:', error)
      this.setData({ errorMessage: '网络异常，请重试' })
    } finally {
      this.setData({ loading: false })
    }
  }
})
