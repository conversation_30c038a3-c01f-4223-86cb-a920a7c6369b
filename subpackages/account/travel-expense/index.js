// subpackages/account/travel-expense/index.js

import simpleDataManager from '../../../utils/simple-data-manager.js'
import layeredSyncManager from '../../../utils/layered-sync-manager.js'
import storage from '../../../utils/storage.js'
import auth from '../../../utils/auth.js'
import cloudRunWebSocketManager from '../../../utils/cloudrun-websocket-manager.js'
import websocketExpenseSyncManager from '../../../utils/websocket-expense-sync-manager.js'

const app = getApp();

Page({
  data: {
    // 基础状态
    recordMode: 'daily', // daily | travel - 默认日常记账
    expenseType: 'expense', // expense | income
    amount: '',
    description: '',
    selectedDate: '',
    selectedCategory: '',
    selectedPlan: null,
    selectedLocation: null,
    photos: [],
    planId: null, // 旅行计划ID
    
    // UI状态
    showModePanel: false,
    amountFocus: false,
    loading: false,
    loadingText: '保存中...',
    
    // 分类数据
    categories: [],
    categoryTabs: [
      { id: 'common', name: '常用' },
      { id: 'travel', name: '旅行' },
      { id: 'daily', name: '日常' }
    ],
    selectedTab: 'common',
    currentTabCategories: [],
    
    // 提示信息
    amountTips: ''
  },

  onLoad(options) {
    // 检查登录状态
    if (!auth.checkPageAuth(this)) {
      return
    }

    this.initPage();
    this.loadCategories();
    this.setDefaultDate();

    // 如果从其他页面传入参数
    if (options.mode) {
      const mode = options.mode;
      // 旅行模式下强制设置为支出
      const expenseType = mode === 'travel' ? 'expense' : this.data.expenseType;
      this.setData({
        recordMode: mode,
        expenseType: expenseType
      });
    }
    if (options.planId) {
      this.setData({ planId: options.planId });
      this.loadTravelPlan(options.planId);
    }

    // 初始化分类标签页
    this.initCategoryTabs();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadCategories();
  },

  // 初始化页面
  initPage() {
    // 获取用户位置权限
    this.requestLocationPermission();
  },

  // 初始化分类标签页
  initCategoryTabs() {
    // 根据记账模式设置默认标签页
    const defaultTab = this.data.recordMode === 'travel' ? 'travel' : 'common';
    this.setData({
      selectedTab: defaultTab,
      currentTabCategories: this.getCategoriesByTab(defaultTab)
    });
  },

  // 设置默认日期
  setDefaultDate() {
    const today = new Date();
    const dateStr = today.getFullYear() + '-' + 
                   String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(today.getDate()).padStart(2, '0');
    this.setData({ selectedDate: dateStr });
  },

  // 加载分类数据
  loadCategories() {
    const travelCategories = [
      { id: 'transport', name: '交通', icon: 'airplane', color: '#3498DB', main: 'travel', sub: 'transport' },
      { id: 'hotel', name: '住宿', icon: 'hotel', color: '#8E44AD', main: 'travel', sub: 'hotel' },
      { id: 'food', name: '餐饮', icon: 'food', color: '#FFA500', main: 'travel', sub: 'food' },
      { id: 'ticket', name: '门票', icon: 'ticket', color: '#E74C3C', main: 'travel', sub: 'ticket' },
      { id: 'shopping', name: '购物', icon: 'shopping-bag', color: '#FFB6C1', main: 'travel', sub: 'shopping' },
      { id: 'other', name: '其他', icon: 'gift', color: '#95A5A6', main: 'travel', sub: 'other' }
    ];

    const dailyCategories = [
      { id: 'food_daily', name: '餐饮', icon: 'coffee', color: '#D2691E', main: 'daily', sub: 'food' },
      { id: 'transport_daily', name: '交通', icon: 'car', color: '#4ECDC4', main: 'daily', sub: 'transport' },
      { id: 'shopping_daily', name: '购物', icon: 'shopping-bag', color: '#FFB6C1', main: 'daily', sub: 'shopping' },
      { id: 'entertainment', name: '娱乐', icon: 'music', color: '#9B59B6', main: 'daily', sub: 'entertainment' },
      { id: 'medical', name: '医疗', icon: 'shield', color: '#27AE60', main: 'daily', sub: 'medical' },
      { id: 'education', name: '教育', icon: 'bookmark', color: '#F39C12', main: 'daily', sub: 'education' }
    ];

    // 根据记账模式显示不同分类
    const categories = this.data.recordMode === 'travel' ? travelCategories : dailyCategories;
    
    this.setData({ 
      categories: categories.slice(0, 8), // 首页只显示8个
      currentTabCategories: this.getCategoriesByTab('travel')
    });
  },

  // 根据标签获取分类
  getCategoriesByTab(tabId) {
    const allCategories = {
      common: [
        { id: 'food', name: '餐饮', icon: 'food', color: '#FFA500' },
        { id: 'transport', name: '交通', icon: 'airplane', color: '#3498DB' },
        { id: 'shopping', name: '购物', icon: 'shopping-bag', color: '#FFB6C1' },
        { id: 'entertainment', name: '娱乐', icon: 'music', color: '#9B59B6' }
      ],
      travel: [
        { id: 'transport', name: '交通', icon: 'car', color: '#3498DB' },
        { id: 'hotel', name: '住宿', icon: 'home', color: '#8E44AD' },
        { id: 'food', name: '餐饮', icon: 'coffee', color: '#FFA500' },
        { id: 'ticket', name: '门票', icon: 'bookmark', color: '#E74C3C' },
        { id: 'shopping', name: '购物', icon: 'shopping-bag', color: '#FFB6C1' },
        { id: 'other', name: '其他', icon: 'star', color: '#95A5A6' }
      ],
      daily: [
        { id: 'food_daily', name: '餐饮', icon: 'coffee', color: '#D2691E' },
        { id: 'transport_daily', name: '交通', icon: 'car', color: '#4ECDC4' },
        { id: 'shopping_daily', name: '购物', icon: 'shopping-bag', color: '#FFB6C1' },
        { id: 'entertainment', name: '娱乐', icon: 'entertainment', color: '#9B59B6' },
        { id: 'medical', name: '医疗', icon: 'shield', color: '#27AE60' },
        { id: 'education', name: '教育', icon: 'bookmark', color: '#F39C12' },
        { id: 'house', name: '居住', icon: 'home', color: '#34495E' },
        { id: 'other_daily', name: '其他', icon: 'star', color: '#95A5A6' }
      ]
    };
    
    return allCategories[tabId] || [];
  },

  // 导航返回
  navigateBack() {
    wx.navigateBack();
  },

  // 显示模式选择面板
  showModeSelector() {
    this.setData({ showModePanel: true });
  },

  // 隐藏模式选择面板
  hideModeSelector() {
    this.setData({ showModePanel: false });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 切换记账模式
  switchMode(e) {
    const mode = e.currentTarget.dataset.mode;
    const defaultTab = mode === 'travel' ? 'travel' : 'common';

    // 旅行模式下强制设置为支出
    const expenseType = mode === 'travel' ? 'expense' : this.data.expenseType;

    this.setData({
      recordMode: mode,
      expenseType: expenseType,
      showModePanel: false,
      selectedCategory: '', // 重置分类选择
      selectedTab: defaultTab,
      currentTabCategories: this.getCategoriesByTab(defaultTab)
    });
    this.loadCategories();

    // 显示切换成功提示
    wx.showToast({
      title: mode === 'daily' ? '已切换到日常记账' : '已切换到旅行记账',
      icon: 'success',
      duration: 1500
    });
  },

  // 切换收入/支出类型
  switchType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({ expenseType: type });
    
    // 触觉反馈
    wx.vibrateShort();
  },

  // 金额输入
  onAmountInput(e) {
    let value = e.detail.value;
    
    // 限制小数点后两位
    if (value.includes('.')) {
      const parts = value.split('.');
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
    }
    
    // 生成智能提示
    let tips = '';
    const amount = parseFloat(value);
    if (amount > 1000) {
      tips = '这是一笔较大的支出，请确认金额正确';
    } else if (amount > 0 && amount < 1) {
      tips = '金额较小，建议合并记录';
    }
    
    this.setData({ 
      amount: value,
      amountTips: tips
    });
    
    // 智能分类建议
    this.suggestCategory(value);
  },

  // 智能分类建议
  suggestCategory(amount) {
    // 基于金额范围的智能分类建议
    const amountNum = parseFloat(amount);
    if (amountNum > 500 && this.data.recordMode === 'travel') {
      // 大额支出可能是住宿或交通
      if (!this.data.selectedCategory) {
        // 可以在这里添加分类建议逻辑
      }
    }
  },

  // 选择分类
  selectCategory(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({ selectedCategory: categoryId });
    
    // 触觉反馈
    wx.vibrateShort();
  },

  // 描述输入
  onDescriptionInput(e) {
    this.setData({ description: e.detail.value });
  },

  // 日期选择
  onDateChange(e) {
    this.setData({ selectedDate: e.detail.value });
  },

  // 请求位置权限
  requestLocationPermission() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.userLocation']) {
          // 不在这里主动申请权限，而是在用户点击位置功能时再申请
        }
      }
    });
  },

  // 选择旅行计划
  selectTravelPlan() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/plan-list/index?mode=select',
      success: () => {
        // 监听计划选择结果
        wx.onAppRoute((res) => {
          if (res.path === 'subpackages/account/travel-expense/index' && res.query.selectedPlan) {
            this.setData({ selectedPlan: JSON.parse(res.query.selectedPlan) });
          }
        });
      }
    });
  },

  // 选择位置
  selectLocation() {
    // 先检查权限状态
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === false) {
          // 用户之前拒绝过，需要引导到设置页面
          wx.showModal({
            title: '需要位置权限',
            content: '为了记录您的消费地点，需要获取位置权限。请在设置中开启位置权限。',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 权限开启后，重新调用位置选择
                      this.doChooseLocation();
                    }
                  }
                });
              }
            }
          });
        } else if (res.authSetting['scope.userLocation'] === undefined) {
          // 用户还没有被询问过权限，先申请权限
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.doChooseLocation();
            },
            fail: () => {
              wx.showToast({
                title: '需要位置权限才能使用此功能',
                icon: 'none'
              });
            }
          });
        } else {
          // 已经有权限，直接选择位置
          this.doChooseLocation();
        }
      }
    });
  },

  // 执行位置选择
  doChooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          selectedLocation: {
            name: res.name || '未知地点',
            address: res.address || '地址信息不完整',
            latitude: res.latitude,
            longitude: res.longitude
          }
        });

        wx.showToast({
          title: '位置选择成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        if (err.errMsg.includes('cancel')) {
          // 用户取消选择
          return;
        }

        wx.showToast({
          title: '位置选择失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 添加照片
  addPhoto() {
    const that = this;
    wx.chooseMedia({
      count: 9 - this.data.photos.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath);
        const newPhotos = [...this.data.photos, ...tempFiles];
        this.setData({ photos: newPhotos });

        wx.showToast({
          title: '照片添加成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        if (err.errMsg.includes('cancel')) {
          return; // 用户取消
        }

        wx.showToast({
          title: '照片选择失败',
          icon: 'none'
        });
      }
    });
  },

  // 预览照片
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.photos[index],
      urls: this.data.photos
    });
  },

  // 删除照片
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index;
    const photos = [...this.data.photos];
    photos.splice(index, 1);
    this.setData({ photos });
  },



  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 切换分类标签
  switchCategoryTab(e) {
    const tabId = e.currentTarget.dataset.id;
    this.setData({
      selectedTab: tabId,
      currentTabCategories: this.getCategoriesByTab(tabId)
    });
  },



  // 处理语音识别结果
  onVoiceResult(e) {
    const { amount, category, description, voiceText } = e.detail

    // 自动填充金额
    if (amount) {
      this.setData({
        amount: amount.toString(),
        amountFocus: false
      })
    }

    // 自动选择分类
    if (category) {
      this.setData({
        selectedCategory: category
      })
    }

    // 自动填充描述
    if (description) {
      this.setData({
        description: description
      })
    }

    // 显示语音识别成功提示
    wx.showToast({
      title: '语音识别成功',
      icon: 'success',
      duration: 1500
    })

    // 触觉反馈
    wx.vibrateShort()

    // 如果所有必要信息都已填充，可以提示用户保存
    if (amount && category) {
      setTimeout(() => {
        wx.showToast({
          title: '信息已自动填充，请确认后保存',
          icon: 'none',
          duration: 2000
        })
      }, 1600)
    }
  },

  // 保存记录（使用WebSocket同步）
  async saveRecord() {
    // 验证必填字段
    if (!this.data.amount) {
      wx.showToast({
        title: '请输入金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedCategory) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      });
      return;
    }

    try {
      // 准备记录数据
      const categoryInfo = this.getCategoryById(this.data.selectedCategory);
      const recordData = {
        type: this.data.expenseType,
        amount: parseFloat(this.data.amount),
        category: categoryInfo,
        description: this.data.description,
        date: this.data.selectedDate,
        location: this.data.selectedLocation,
        planId: this.data.planId || this.data.selectedPlan?._id || null,
        images: [], // 先不上传图片，后台处理
        mode: this.data.recordMode,
        createTime: new Date().toISOString()
      };

      // 显示保存中状态
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 使用WebSocket同步管理器保存
      const result = await websocketExpenseSyncManager.addExpenseRecord(recordData);

      wx.hideLoading();

      if (result.success) {
        // 显示真实的保存状态
        wx.showToast({
          title: result.message,
          icon: 'success',
          duration: 2000
        });

        // 触觉反馈
        wx.vibrateShort();

        // 更新本地财务数据缓存
        this.updateLocalFinancialCache(recordData);

        // 延迟返回，让用户看到状态
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);

      } else {
        // 显示保存失败
        wx.showModal({
          title: '保存失败',
          content: result.message || '记录保存失败，请重试',
          showCancel: false,
          confirmText: '知道了'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('保存记录失败:', error);

      wx.showModal({
        title: '保存失败',
        content: '网络错误，请检查网络连接后重试',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // 更新本地财务缓存
  updateLocalFinancialCache(recordData) {
    try {
      // 获取当前财务数据
      const currentData = simpleDataManager.getFromMemoryCache('financial_overview') ||
                         simpleDataManager.getFromLocalStorage('financial_overview');

      if (currentData) {
        // 本地计算更新财务数据
        const updatedData = {
          ...currentData,
          totalExpense: (currentData.totalExpense || 0) + recordData.amount,
          // 根据记录模式更新对应支出
          ...(recordData.mode === 'travel' ? {
            travelExpense: (currentData.travelExpense || 0) + recordData.amount
          } : {
            dailyExpense: (currentData.dailyExpense || 0) + recordData.amount
          })
        };

        // 更新缓存
        simpleDataManager.setMemoryCache('financial_overview', updatedData);
        simpleDataManager.setLocalStorage('financial_overview', updatedData);
      }
    } catch (error) {
      console.warn('更新本地财务缓存失败:', error);
    }
  },

  // 根据ID获取分类信息
  getCategoryById(categoryId) {
    const allCategories = [
      ...this.getCategoriesByTab('common'),
      ...this.getCategoriesByTab('travel'),
      ...this.getCategoriesByTab('daily')
    ];

    return allCategories.find(cat => cat.id === categoryId) || null;
  },

  // 上传照片到云存储
  async uploadPhotos() {
    if (this.data.photos.length === 0) return [];

    const uploadPromises = this.data.photos.map(async (photo, index) => {
      const cloudPath = `expense-photos/${Date.now()}-${index}.jpg`;
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath: photo
      });
      return result.fileID;
    });

    return Promise.all(uploadPromises);
  },



  // 加载旅行计划
  async loadTravelPlan(planId) {
    try {
      // 使用云函数获取计划信息，支持协作者权限
      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'getTravelPlan',
          data: { planId }
        }
      })

      if (result.result && result.result.success) {
        this.setData({ selectedPlan: result.result.data });
      } else {
        wx.showToast({
          title: result.result?.message || '无法加载计划信息',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.showToast({
        title: '加载计划信息失败',
        icon: 'none'
      })
    }
  },

  // 导航到历史记录页面
  navigateToHistory() {
    wx.navigateTo({
      url: '/subpackages/account/expense-list/index',
      fail: (err) => {
        console.error('导航失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 发送支出WebSocket更新通知
   */
  sendExpenseWebSocketUpdate(recordData) {
    try {
      // 检查WebSocket连接状态
      const status = cloudRunWebSocketManager.getStatus();
      if (!status.isConnected) {
        console.log('WebSocket未连接，跳过支出更新通知');
        return;
      }

      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.openid) {
        console.log('用户信息不完整，跳过WebSocket通知');
        return;
      }

      // 构建更新数据
      const updateData = {
        planId: recordData.planId,
        updateType: 'add',
        expenseData: {
          id: recordData.id || `temp_${Date.now()}`,
          description: recordData.description,
          amount: recordData.amount,
          category: recordData.category,
          date: recordData.date,
          location: recordData.location,
          operatorName: userInfo.nickName || '用户',
          createTime: Date.now()
        }
      };

      // 发送WebSocket更新
      cloudRunWebSocketManager.send(updateData, 'expense_update');

      console.log('支出WebSocket更新通知已发送:', updateData);

    } catch (error) {
      console.error('发送支出WebSocket更新失败:', error);
      // 不影响主流程，静默处理错误
    }
  },


});
